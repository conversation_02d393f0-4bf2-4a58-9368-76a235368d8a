import { Component } from '@angular/core';
import { Router } from '@angular/router';

interface FormData {
  name: string;
  email: string;
  age: number | null;
  gender: string;
  comments: string;
}

interface AnalysisResult {
  primaryType: string;
  primaryTypeRoute: string;
  fleurPercentage: number;
  bijouPercentage: number;
  fluxPercentage: number;
  shakerPercentage: number;
  description: string;
}

@Component({
  selector: 'app-iris-form',
  templateUrl: './iris-form.component.html',
  styleUrls: ['./iris-form.component.scss']
})
export class IrisFormComponent {
  previewUrl: string | null = null;
  isAnalyzing: boolean = false;
  analysisResult: AnalysisResult | null = null;

  formData: FormData = {
    name: '',
    email: '',
    age: null,
    gender: '',
    comments: ''
  };

  constructor(private router: Router) {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFile(file);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      if (file.type.match('image.*')) {
        this.handleFile(file);
      }
    }
  }

  handleFile(file: File): void {
    if (file.type.match('image.*')) {
      const reader = new FileReader();

      reader.onload = (e: any) => {
        this.previewUrl = e.target.result;
      };

      reader.readAsDataURL(file);
    }
  }

  removeImage(event: Event): void {
    event.stopPropagation();
    this.previewUrl = null;
  }

  analyzeIris(): void {
    if (!this.previewUrl) return;

    this.isAnalyzing = true;

    // Simuler une analyse d'IA avec un délai
    setTimeout(() => {
      // Générer des résultats aléatoires pour la démonstration
      const types = ['Fleur', 'Bijou', 'Flux', 'Shaker'];
      const routes = ['fleur', 'bijou', 'flux', 'shaker'];

      // Générer des pourcentages aléatoires qui totalisent 100%
      let fleur = Math.floor(Math.random() * 100);
      let bijou = Math.floor(Math.random() * (100 - fleur));
      let flux = Math.floor(Math.random() * (100 - fleur - bijou));
      let shaker = 100 - fleur - bijou - flux;

      // Déterminer le type principal
      const percentages = [fleur, bijou, flux, shaker];
      const maxIndex = percentages.indexOf(Math.max(...percentages));

      const descriptions = [
        "Votre iris révèle une forte dominante du type Fleur, indiquant une personnalité émotionnelle, créative et expressive. Vous êtes probablement sensible aux paroles et avez un mode d'apprentissage auditif.",
        "Votre iris montre une prédominance du type Bijou, suggérant une personnalité analytique et réfléchie. Vous apprenez probablement mieux par l'observation et la lecture.",
        "L'analyse de votre iris indique une dominante du type Flux, révélant une nature intuitive, physique et empathique. Votre apprentissage est principalement kinesthésique.",
        "Votre iris présente les caractéristiques du type Shaker, indiquant une personnalité motivée, expressive et orientée vers l'action. Vous êtes probablement énergique et innovant."
      ];

      this.analysisResult = {
        primaryType: types[maxIndex],
        primaryTypeRoute: routes[maxIndex],
        fleurPercentage: fleur,
        bijouPercentage: bijou,
        fluxPercentage: flux,
        shakerPercentage: shaker,
        description: descriptions[maxIndex]
      };

      this.isAnalyzing = false;
    }, 3000); // Simuler un délai de 3 secondes pour l'analyse
  }
}
