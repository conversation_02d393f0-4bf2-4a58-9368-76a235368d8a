import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccueilComponent } from './accueil/accueil.component';
import { SuivantaccComponent } from './suivantacc/suivantacc.component';
import { TypeirisComponent } from './typeiris/typeiris.component';
import { Iris2Component } from './iris2/iris2.component';
import { FleurComponent } from './fleur/fleur.component';
import { BijouComponent } from './bijou/bijou.component';
import { FluxComponent } from './flux/flux.component';
import { ShakerComponent } from './shaker/shaker.component';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { PersonalityTestComponent } from './personality-test/personality-test.component';

const routes: Routes = [
  { path: 'accueil', component: AccueilComponent },  // Page d'accueil
  { path: 'suivantacc', component: SuivantaccComponent },  // Page après avoir cliqué sur "Commencer"
  { path: 'typeiris', component: TypeirisComponent },  // Page après avoir cliqué sur "Suivant"
  { path: 'iris2' , component: Iris2Component},
  { path: 'iris-diversity', component: IrisDiversityComponent },  // Page de diversité des iris
  { path: 'fleur', component: FleurComponent },
  { path: 'bijou', component: BijouComponent},
  { path: 'flux', component: FluxComponent },
  { path: 'shaker', component: ShakerComponent },
  { path: 'login', component: LoginComponent },  // Page de connexion
  { path: 'signup', component: SignupComponent },  // Page d'inscription
  { path: 'dashboard', component: DashboardComponent },  // Tableau de bord utilisateur
  { path: 'personality-test', component: PersonalityTestComponent },  // Test de personnalité
  { path: '', redirectTo: '/accueil', pathMatch: 'full' },  // Rediriger vers la page d'accueil par défaut
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
