.iris-form-container {
  padding: 60px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  min-height: 100vh;
  font-family: 'Montserrat', sans-serif;

  .form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px;

    .form-header {
      text-align: center;
      margin-bottom: 40px;

      .title {
        font-family: 'Playfair Display', serif;
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 10px;
      }

      .subtitle {
        color: #666;
        font-size: 1.1rem;
        margin-bottom: 15px;
      }

      .divider {
        width: 80px;
        height: 3px;
        background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary), var(--flux-primary), var(--shaker-primary));
        margin: 0 auto;
        border-radius: 3px;
      }
    }

    .form-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      margin-bottom: 40px;

      .upload-section {
        .upload-container {
          width: 100%;
          height: 300px;
          border: 2px dashed #ccc;
          border-radius: 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          margin-bottom: 20px;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--fleur-primary);
            background-color: rgba(138, 79, 255, 0.03);
          }

          &.has-image {
            border: none;
          }

          .upload-placeholder {
            text-align: center;
            padding: 20px;

            .upload-icon {
              font-size: 3rem;
              color: #aaa;
              margin-bottom: 15px;
            }

            .upload-text {
              font-size: 1.1rem;
              color: #666;
              margin-bottom: 10px;
            }

            .upload-text-small {
              font-size: 0.9rem;
              color: #999;
            }
          }

          .preview-container {
            width: 100%;
            height: 100%;
            position: relative;

            .preview-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 15px;
            }

            .remove-btn {
              position: absolute;
              top: 10px;
              right: 10px;
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background-color: rgba(255, 255, 255, 0.8);
              border: none;
              font-size: 1.2rem;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background-color: white;
                transform: scale(1.1);
              }
            }
          }
        }

        .upload-instructions {
          background-color: rgba(255, 255, 255, 0.7);
          padding: 20px;
          border-radius: 15px;
          border-left: 3px solid var(--flux-primary);

          h3 {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 15px;
          }

          ul {
            padding-left: 20px;

            li {
              font-size: 0.9rem;
              color: #666;
              margin-bottom: 8px;
            }
          }
        }
      }

      .form-fields {
        .form-group {
          margin-bottom: 20px;

          label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
          }

          input[type="text"],
          input[type="email"],
          input[type="number"],
          textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;

            &:focus {
              outline: none;
              border-color: var(--bijou-primary);
              box-shadow: 0 0 0 3px rgba(79, 138, 255, 0.1);
            }
          }

          textarea {
            min-height: 120px;
            resize: vertical;
          }

          .radio-group {
            display: flex;
            gap: 20px;

            label {
              display: flex;
              align-items: center;
              cursor: pointer;

              input[type="radio"] {
                margin-right: 8px;
              }
            }
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      margin-bottom: 40px;

      .submit-btn {
        padding: 15px 40px;
        background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
        color: white;
        border: none;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);

        &:hover:not(:disabled) {
          transform: translateY(-3px);
          box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .result-section {
      margin-top: 50px;

      .result-card {
        background-color: #f8f9fa;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);

        .result-title {
          font-family: 'Playfair Display', serif;
          font-size: 1.8rem;
          color: #333;
          text-align: center;
          margin-bottom: 15px;
        }

        .divider {
          width: 60px;
          height: 3px;
          background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary), var(--flux-primary), var(--shaker-primary));
          margin: 0 auto 30px;
          border-radius: 3px;
        }

        .result-content {
          .result-type {
            text-align: center;
            margin-bottom: 30px;

            h4 {
              font-size: 1.1rem;
              color: #555;
              margin-bottom: 10px;
            }

            .type-name {
              font-size: 2rem;
              font-weight: 700;
              background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary), var(--flux-primary), var(--shaker-primary));
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          .result-chart {
            margin-bottom: 30px;

            .chart-bar {
              display: flex;
              align-items: center;
              margin-bottom: 15px;

              .bar-label {
                width: 60px;
                font-size: 0.9rem;
                font-weight: 500;
                color: #555;
              }

              .bar-container {
                flex: 1;
                height: 12px;
                background-color: #eee;
                border-radius: 6px;
                overflow: hidden;
                margin: 0 15px;

                .bar-fill {
                  height: 100%;
                  border-radius: 6px;
                  transition: width 1s ease-in-out;

                  &.fleur {
                    background-color: var(--fleur-primary);
                  }

                  &.bijou {
                    background-color: var(--bijou-primary);
                  }

                  &.flux {
                    background-color: var(--flux-primary);
                  }

                  &.shaker {
                    background-color: var(--shaker-primary);
                  }
                }
              }

              .bar-value {
                width: 40px;
                font-size: 0.9rem;
                font-weight: 600;
                color: #333;
                text-align: right;
              }
            }
          }

          .result-description {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;

            p {
              font-size: 1rem;
              line-height: 1.6;
              color: #555;
            }
          }

          .result-actions {
            display: flex;
            justify-content: center;
            gap: 20px;

            .learn-more-btn, .share-btn {
              padding: 12px 25px;
              border-radius: 50px;
              font-size: 1rem;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.3s ease;
            }

            .learn-more-btn {
              background-color: var(--bijou-primary);
              color: white;
              border: none;
              box-shadow: 0 5px 15px rgba(79, 138, 255, 0.3);

              &:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 20px rgba(79, 138, 255, 0.4);
              }
            }

            .share-btn {
              background-color: white;
              color: #555;
              border: 1px solid #ddd;

              &:hover {
                background-color: #f8f9fa;
                transform: translateY(-3px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
              }
            }
          }
        }
      }
    }
  }
}

// Media queries pour la responsivité
@media (max-width: 992px) {
  .iris-form-container {
    .form-card {
      padding: 30px;

      .form-content {
        grid-template-columns: 1fr;
        gap: 30px;
      }
    }
  }
}

@media (max-width: 768px) {
  .iris-form-container {
    .form-card {
      padding: 20px;

      .form-header {
        .title {
          font-size: 1.8rem;
        }
      }

      .form-content {
        .upload-section {
          .upload-container {
            height: 250px;
          }
        }
      }

      .result-section {
        .result-card {
          padding: 20px;

          .result-content {
            .result-actions {
              flex-direction: column;
              gap: 15px;

              .learn-more-btn, .share-btn {
                width: 100%;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
}