import { Component } from '@angular/core';
import { Router } from '@angular/router';

interface LoginData {
  email: string;
  password: string;
  rememberMe: boolean;
}

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  loginData: LoginData = {
    email: '',
    password: '',
    rememberMe: false
  };

  showPassword: boolean = false;
  isLoading: boolean = false;

  // Comptes statiques pour les tests
  staticAccounts = [
    {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Administrateur Test',
      role: 'admin'
    },
    {
      email: '<EMAIL>',
      password: 'user123',
      name: 'Utilisateur Test',
      role: 'user'
    },
    {
      email: '<EMAIL>',
      password: 'marie123',
      name: '<PERSON>',
      role: 'user'
    },
    {
      email: '<EMAIL>',
      password: 'jean123',
      name: '<PERSON>',
      role: 'user'
    }
  ];

  constructor(private router: Router) {}

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onSubmit(): void {
    if (this.isLoading) return;

    this.isLoading = true;

    // Simuler une connexion avec un délai
    setTimeout(() => {
      console.log('Tentative de connexion avec:', this.loginData);

      // Vérifier si les identifiants correspondent à un compte statique
      const account = this.staticAccounts.find(acc =>
        acc.email === this.loginData.email && acc.password === this.loginData.password
      );

      if (account) {
        console.log('Connexion réussie avec le compte:', account.name);
        // Sauvegarder les informations de l'utilisateur connecté
        localStorage.setItem('currentUser', JSON.stringify({
          name: account.name,
          email: account.email,
          role: account.role
        }));

        // Rediriger selon le rôle
        if (account.role === 'admin') {
          this.router.navigate(['/dashboard']);
        } else {
          // Les utilisateurs normaux vont directement au test de personnalité
          this.router.navigate(['/personality-test']);
        }
      } else {
        console.log('Identifiants incorrects');
        alert('Email ou mot de passe incorrect. Utilisez un des comptes de test.');
      }

      this.isLoading = false;
    }, 1500);
  }

  /**
   * Remplit automatiquement le formulaire avec un compte de test
   */
  fillTestAccount(account: any): void {
    this.loginData.email = account.email;
    this.loginData.password = account.password;
  }
}
