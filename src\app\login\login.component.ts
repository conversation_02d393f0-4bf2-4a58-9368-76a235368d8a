import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { IrisImageService, IrisImageData, IrisAnalysisResult } from '../services/iris-image.service';

interface LoginData {
  email: string;
  password: string;
  rememberMe: boolean;
}

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  loginData: LoginData = {
    email: '',
    password: '',
    rememberMe: false
  };

  showPassword: boolean = false;
  isLoading: boolean = false;

  // Variables pour l'image d'iris
  selectedIrisFile: File | null = null;
  irisPreviewUrl: string | null = null;
  isAnalyzingIris: boolean = false;
  irisAnalysisResult: IrisAnalysisResult | null = null;
  showIrisSection: boolean = false;

  // Comptes statiques pour les tests
  staticAccounts = [
    {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Administrateur Test',
      role: 'admin'
    },
    {
      email: '<EMAIL>',
      password: 'user123',
      name: 'Utilisateur Test',
      role: 'user'
    },
    {
      email: '<EMAIL>',
      password: 'marie123',
      name: 'Marie Dubois',
      role: 'user'
    },
    {
      email: '<EMAIL>',
      password: 'jean123',
      name: 'Jean Martin',
      role: 'user'
    }
  ];

  constructor(
    private router: Router,
    private irisImageService: IrisImageService
  ) {}

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onSubmit(): void {
    if (this.isLoading) return;

    this.isLoading = true;

    // Simuler une connexion avec un délai
    setTimeout(() => {
      console.log('Tentative de connexion avec:', this.loginData);

      // Vérifier si les identifiants correspondent à un compte statique
      const account = this.staticAccounts.find(acc =>
        acc.email === this.loginData.email && acc.password === this.loginData.password
      );

      if (account) {
        console.log('Connexion réussie avec le compte:', account.name);

        // Sauvegarder l'image d'iris si elle a été analysée
        if (this.selectedIrisFile && this.irisAnalysisResult) {
          this.saveIrisImage(account.email, account.name);
        }

        // Sauvegarder les informations de l'utilisateur connecté
        localStorage.setItem('currentUser', JSON.stringify({
          name: account.name,
          email: account.email,
          role: account.role,
          hasIrisImage: this.selectedIrisFile !== null,
          irisAnalysis: this.irisAnalysisResult
        }));

        // Rediriger selon le rôle
        if (account.role === 'admin') {
          this.router.navigate(['/dashboard']);
        } else {
          // Les utilisateurs normaux vont directement au test de personnalité
          this.router.navigate(['/personality-test']);
        }
      } else {
        console.log('Identifiants incorrects');
        alert('Email ou mot de passe incorrect. Utilisez un des comptes de test.');
      }

      this.isLoading = false;
    }, 1500);
  }

  /**
   * Remplit automatiquement le formulaire avec un compte de test
   */
  fillTestAccount(account: any): void {
    this.loginData.email = account.email;
    this.loginData.password = account.password;
  }

  /**
   * Affiche/masque la section d'upload d'iris
   */
  toggleIrisSection(): void {
    this.showIrisSection = !this.showIrisSection;
  }

  /**
   * Gère la sélection d'un fichier d'iris
   */
  onIrisFileSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Valider le fichier
    const validation = this.irisImageService.validateImageFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    this.selectedIrisFile = file;

    // Créer un aperçu de l'image
    const reader = new FileReader();
    reader.onload = (e: any) => {
      this.irisPreviewUrl = e.target.result;
    };
    reader.readAsDataURL(file);

    console.log('📸 Image d\'iris sélectionnée:', file.name);
  }

  /**
   * Analyse l'image d'iris
   */
  async analyzeIris(): Promise<void> {
    if (!this.selectedIrisFile) {
      alert('Veuillez d\'abord sélectionner une image d\'iris');
      return;
    }

    this.isAnalyzingIris = true;
    console.log('🔍 Début de l\'analyse de l\'iris...');

    try {
      // Convertir l'image en base64
      const imageBase64 = await this.irisImageService.convertImageToBase64(this.selectedIrisFile);

      // Obtenir les dimensions
      const dimensions = await this.irisImageService.getImageDimensions(this.selectedIrisFile);

      // Analyser l'iris
      this.irisAnalysisResult = await this.irisImageService.analyzeIrisImage(imageBase64);

      console.log('✅ Analyse terminée:', this.irisAnalysisResult);

    } catch (error) {
      console.error('❌ Erreur lors de l\'analyse:', error);
      alert('Erreur lors de l\'analyse de l\'iris. Veuillez réessayer.');
    } finally {
      this.isAnalyzingIris = false;
    }
  }

  /**
   * Sauvegarde l'image d'iris lors de la connexion
   */
  private async saveIrisImage(userEmail: string, userName: string): Promise<void> {
    if (!this.selectedIrisFile || !this.irisAnalysisResult) return;

    try {
      const imageBase64 = await this.irisImageService.convertImageToBase64(this.selectedIrisFile);
      const dimensions = await this.irisImageService.getImageDimensions(this.selectedIrisFile);

      const irisData: IrisImageData = {
        userEmail: userEmail,
        userName: userName,
        imageUrl: '', // Pas d'URL externe pour l'instant
        imageBase64: imageBase64,
        uploadedAt: new Date(),
        analysisResult: this.irisAnalysisResult,
        metadata: {
          fileName: this.selectedIrisFile.name,
          fileSize: this.selectedIrisFile.size,
          fileType: this.selectedIrisFile.type,
          imageWidth: dimensions.width,
          imageHeight: dimensions.height
        }
      };

      // Sauvegarder dans Firebase
      this.irisImageService.saveIrisImage(irisData).subscribe({
        next: (docId) => {
          console.log('✅ Image d\'iris sauvegardée avec l\'ID:', docId);
        },
        error: (error) => {
          console.error('❌ Erreur sauvegarde iris:', error);
        }
      });

    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde de l\'iris:', error);
    }
  }

  /**
   * Supprime l'image sélectionnée
   */
  removeIrisImage(): void {
    this.selectedIrisFile = null;
    this.irisPreviewUrl = null;
    this.irisAnalysisResult = null;

    // Reset du input file
    const fileInput = document.getElementById('irisFile') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }
}
