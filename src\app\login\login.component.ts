import { Component } from '@angular/core';
import { Router } from '@angular/router';

interface LoginData {
  email: string;
  password: string;
  rememberMe: boolean;
}

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  loginData: LoginData = {
    email: '',
    password: '',
    rememberMe: false
  };

  showPassword: boolean = false;
  isLoading: boolean = false;

  constructor(private router: Router) {}

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onSubmit(): void {
    if (this.isLoading) return;

    this.isLoading = true;

    // Simuler une connexion avec un délai
    setTimeout(() => {
      console.log('Tentative de connexion avec:', this.loginData);

      // Ici, vous implémenteriez la logique réelle de connexion
      // Pour l'instant, nous simulons simplement une connexion réussie

      // Rediriger vers le tableau de bord après connexion
      this.router.navigate(['/dashboard']);

      this.isLoading = false;
    }, 1500);
  }
}
