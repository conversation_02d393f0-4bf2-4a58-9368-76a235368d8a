import { Injectable } from '@angular/core';
import { Firestore, doc, setDoc, getDoc, collection, addDoc } from '@angular/fire/firestore';
import { Observable, from } from 'rxjs';

export interface IrisImageData {
  id?: string;
  userEmail: string;
  userName: string;
  imageUrl: string;
  imageBase64: string;
  uploadedAt: Date;
  analysisResult?: IrisAnalysisResult;
  metadata: {
    fileName: string;
    fileSize: number;
    fileType: string;
    imageWidth?: number;
    imageHeight?: number;
  };
}

export interface IrisAnalysisResult {
  irisType: string;
  characteristics: string[];
  dominantColors: string[];
  patterns: string[];
  confidence: number;
  compatibilityScore: number;
  recommendations: string[];
}

@Injectable({
  providedIn: 'root'
})
export class IrisImageService {

  constructor(private firestore: Firestore) {}

  /**
   * Convertit un fichier image en base64
   */
  convertImageToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsDataURL(file);
    });
  }

  /**
   * Valide le fichier image
   */
  validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Vérifier le type de fichier
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Format non supporté. Utilisez JPG, PNG ou WebP.'
      };
    }

    // Vérifier la taille (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'L\'image est trop volumineuse. Maximum 5MB.'
      };
    }

    return { isValid: true };
  }

  /**
   * Obtient les dimensions de l'image
   */
  getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Impossible de charger l\'image'));
      };
      
      img.src = url;
    });
  }

  /**
   * Sauvegarde l'image d'iris dans Firestore
   */
  saveIrisImage(irisData: IrisImageData): Observable<string> {
    const irisCollection = collection(this.firestore, 'iris_images');
    
    const dataToSave = {
      ...irisData,
      uploadedAt: irisData.uploadedAt.toISOString()
    };

    return from(addDoc(irisCollection, dataToSave).then(docRef => docRef.id));
  }

  /**
   * Récupère l'image d'iris d'un utilisateur
   */
  getUserIrisImage(userEmail: string): Observable<IrisImageData | null> {
    const irisDoc = doc(this.firestore, 'iris_images', userEmail);
    
    return from(getDoc(irisDoc).then(docSnap => {
      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          id: docSnap.id,
          ...data,
          uploadedAt: new Date(data['uploadedAt'])
        } as IrisImageData;
      }
      return null;
    }));
  }

  /**
   * Met à jour l'image d'iris d'un utilisateur
   */
  updateUserIrisImage(userEmail: string, irisData: Partial<IrisImageData>): Observable<boolean> {
    const irisDoc = doc(this.firestore, 'iris_images', userEmail);
    
    const dataToUpdate = {
      ...irisData,
      updatedAt: new Date().toISOString()
    };

    return from(setDoc(irisDoc, dataToUpdate, { merge: true }).then(() => true).catch(() => false));
  }

  /**
   * Analyse simulée de l'iris (à remplacer par une vraie IA)
   */
  analyzeIrisImage(imageBase64: string): Promise<IrisAnalysisResult> {
    return new Promise((resolve) => {
      // Simulation d'analyse avec délai
      setTimeout(() => {
        // Analyse simulée basée sur des patterns dans l'image
        const mockAnalysis: IrisAnalysisResult = {
          irisType: this.detectIrisType(imageBase64),
          characteristics: this.detectCharacteristics(imageBase64),
          dominantColors: this.detectColors(imageBase64),
          patterns: this.detectPatterns(imageBase64),
          confidence: Math.floor(Math.random() * 30) + 70, // 70-100%
          compatibilityScore: Math.floor(Math.random() * 40) + 60, // 60-100%
          recommendations: this.generateRecommendations()
        };
        
        resolve(mockAnalysis);
      }, 2000); // Simulation de 2 secondes d'analyse
    });
  }

  /**
   * Détection simulée du type d'iris
   */
  private detectIrisType(imageBase64: string): string {
    const types = ['Flower', 'Jewel', 'Shaker', 'Stream'];
    // Simulation basée sur la longueur de l'image
    const index = imageBase64.length % types.length;
    return types[index];
  }

  /**
   * Détection simulée des caractéristiques
   */
  private detectCharacteristics(imageBase64: string): string[] {
    const allCharacteristics = [
      'Fibres radiales prononcées',
      'Anneaux concentriques',
      'Pigmentation uniforme',
      'Cryptes visibles',
      'Lacunes périphériques',
      'Collerette bien définie',
      'Pupille régulière',
      'Texture fine'
    ];
    
    // Retourner 3-5 caractéristiques aléatoires
    const count = Math.floor(Math.random() * 3) + 3;
    const shuffled = allCharacteristics.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Détection simulée des couleurs
   */
  private detectColors(imageBase64: string): string[] {
    const colors = [
      ['Bleu', 'Gris'],
      ['Marron', 'Noisette'],
      ['Vert', 'Doré'],
      ['Gris', 'Bleu clair'],
      ['Marron foncé', 'Ambré']
    ];
    
    const index = imageBase64.length % colors.length;
    return colors[index];
  }

  /**
   * Détection simulée des patterns
   */
  private detectPatterns(imageBase64: string): string[] {
    const patterns = [
      'Rayons solaires',
      'Anneaux de croissance',
      'Taches pigmentaires',
      'Fibres en éventail',
      'Cryptes profondes'
    ];
    
    const count = Math.floor(Math.random() * 3) + 2;
    const shuffled = patterns.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Génération de recommandations
   */
  private generateRecommendations(): string[] {
    const recommendations = [
      'Privilégiez les activités créatives pour stimuler votre potentiel',
      'Développez votre capacité d\'analyse et de réflexion',
      'Explorez des environnements dynamiques et stimulants',
      'Cultivez la paix intérieure et la méditation',
      'Renforcez vos relations interpersonnelles',
      'Travaillez sur votre confiance en vous',
      'Explorez de nouveaux défis intellectuels'
    ];
    
    const count = Math.floor(Math.random() * 3) + 2;
    const shuffled = recommendations.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
}
