.auth-container.login {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 40px 0;
  font-family: 'Montserrat', sans-serif;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    margin: 0 auto 40px;
    padding: 40px;

    .auth-header {
      text-align: center;
      margin-bottom: 30px;

      .title {
        font-family: 'Playfair Display', serif;
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 10px;
      }

      .subtitle {
        color: #666;
        font-size: 1rem;
        margin-bottom: 15px;
      }

      .divider {
        width: 60px;
        height: 3px;
        background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
        margin: 0 auto;
        border-radius: 3px;
      }
    }

    .auth-form {
      .form-group {
        margin-bottom: 20px;

        label {
          display: block;
          font-size: 0.9rem;
          font-weight: 500;
          color: #555;
          margin-bottom: 8px;
        }

        .input-container {
          position: relative;
          display: flex;
          align-items: center;

          .input-icon {
            position: absolute;
            left: 15px;
            font-size: 1.1rem;
            color: #aaa;
          }

          input {
            width: 100%;
            padding: 12px 15px 12px 45px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;

            &:focus {
              outline: none;
              border-color: var(--fleur-primary);
              box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);
            }
          }

          .toggle-password {
            position: absolute;
            right: 15px;
            background: none;
            border: none;
            font-size: 1.1rem;
            cursor: pointer;
            color: #aaa;
            transition: all 0.3s ease;

            &:hover {
              color: var(--fleur-primary);
            }
          }
        }

        .error-message {
          font-size: 0.8rem;
          color: #e74c3c;
          margin-top: 5px;

          span {
            display: block;
            margin-bottom: 3px;
          }
        }
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;

        .remember-me {
          display: flex;
          align-items: center;

          input[type="checkbox"] {
            margin-right: 8px;
          }

          label {
            font-size: 0.9rem;
            color: #666;
            cursor: pointer;
          }
        }

        .forgot-password {
          font-size: 0.9rem;
          color: var(--fleur-primary);
          text-decoration: none;
          transition: all 0.3s ease;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .form-actions {
        margin-bottom: 30px;

        .submit-btn {
          width: 100%;
          padding: 14px;
          background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);

          &:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(138, 79, 255, 0.4);
          }

          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
          }
        }
      }

      .social-login {
        margin-bottom: 30px;

        .separator {
          text-align: center;
          position: relative;
          color: #999;
          font-size: 0.9rem;
          margin-bottom: 20px;

          &:before, &:after {
            content: '';
            position: absolute;
            top: 50%;
            width: 40%;
            height: 1px;
            background-color: #ddd;
          }

          &:before {
            left: 0;
          }

          &:after {
            right: 0;
          }
        }

        .social-buttons {
          display: flex;
          gap: 15px;

          .social-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #ddd;
            background-color: white;

            img {
              width: 20px;
              height: 20px;
            }

            .facebook-icon {
              width: 20px;
              height: 20px;
              background-color: #1877f2;
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
            }

            &:hover {
              transform: translateY(-3px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            }

            &.google:hover {
              border-color: #ea4335;
            }

            &.facebook:hover {
              border-color: #1877f2;
            }
          }
        }
      }

      .auth-footer {
        text-align: center;
        font-size: 0.9rem;
        color: #666;

        a {
          color: var(--fleur-primary);
          text-decoration: none;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .navigation {
    display: flex;
    justify-content: center;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 25px;
      background-color: white;
      color: #555;
      border-radius: 50px;
      font-weight: 500;
      text-decoration: none;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        color: var(--fleur-primary);
      }

      .icon {
        font-size: 1.1rem;
      }
    }
  }
}

// Media queries pour la responsivité
@media (max-width: 576px) {
  .auth-container.login {
    padding: 20px 0;

    .auth-card {
      padding: 25px;

      .auth-header {
        .title {
          font-size: 1.8rem;
        }
      }

      .auth-form {
        .form-options {
          flex-direction: column;
          align-items: flex-start;
          gap: 15px;
        }

        .social-login {
          .social-buttons {
            flex-direction: column;
          }
        }
      }
    }
  }
}