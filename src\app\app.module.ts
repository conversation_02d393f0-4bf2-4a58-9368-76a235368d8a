import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

// Firebase imports
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { provideFirestore, getFirestore } from '@angular/fire/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "pfa1",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

import { AccueilComponent } from './accueil/accueil.component';
import { SuivantaccComponent } from './suivantacc/suivantacc.component';
import { TypeirisComponent } from './typeiris/typeiris.component';
import { Iris2Component } from './iris2/iris2.component';
import { FleurComponent } from './fleur/fleur.component';
import { BijouComponent } from './bijou/bijou.component';
import { FluxComponent } from './flux/flux.component';
import { ShakerComponent } from './shaker/shaker.component';
import { IrisFormComponent } from './iris-form/iris-form.component';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { FooterComponent } from './shared/footer/footer.component';
import { PersonalityTestComponent } from './personality-test/personality-test.component';

@NgModule({
  declarations: [
    AppComponent,
    AccueilComponent,
    SuivantaccComponent,
    TypeirisComponent,
    Iris2Component,
    FleurComponent,
    BijouComponent,
    FluxComponent,
    ShakerComponent,
    IrisFormComponent,
    LoginComponent,
    SignupComponent,
    IrisDiversityComponent,
    DashboardComponent,
    FooterComponent,
    PersonalityTestComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    provideFirebaseApp(() => initializeApp(firebaseConfig)),
    provideFirestore(() => getFirestore())
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
