import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms'; // Importez FormsModule
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

import { AccueilComponent } from './accueil/accueil.component';
import { SuivantaccComponent } from './suivantacc/suivantacc.component';
import { TypeirisComponent } from './typeiris/typeiris.component';
import { Iris2Component } from './iris2/iris2.component';
import { FleurComponent } from './fleur/fleur.component';
import { BijouComponent } from './bijou/bijou.component';
import { FluxComponent } from './flux/flux.component';
import { ShakerComponent } from './shaker/shaker.component';
import { IrisFormComponent } from './iris-form/iris-form.component';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { FooterComponent } from './shared/footer/footer.component';

@NgModule({
  declarations: [
    AppComponent,
    AccueilComponent,
    SuivantaccComponent,
    TypeirisComponent,
    Iris2Component,
    FleurComponent,
    BijouComponent,
    FluxComponent,
    ShakerComponent,
    IrisFormComponent,
    LoginComponent,
    SignupComponent,
    IrisDiversityComponent,
    DashboardComponent,
    FooterComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,FormsModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
