import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

// Firebase imports
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { provideDatabase, getDatabase } from '@angular/fire/database';

// Firebase configuration pour PFA1
const firebaseConfig = {
  apiKey: "AIzaSyDummy", // Remplacez par votre vraie clé API
  authDomain: "pfa1-13f62.firebaseapp.com",
  databaseURL: "https://pfa1-13f62-default-rtdb.firebaseio.com/",
  projectId: "pfa1-13f62",
  storageBucket: "pfa1-13f62.appspot.com",
  messagingSenderId: "*********",
  appId: "1:*********:web:dummy"
};

import { AccueilComponent } from './accueil/accueil.component';
import { SuivantaccComponent } from './suivantacc/suivantacc.component';
import { TypeirisComponent } from './typeiris/typeiris.component';
import { Iris2Component } from './iris2/iris2.component';
import { FleurComponent } from './fleur/fleur.component';
import { BijouComponent } from './bijou/bijou.component';
import { FluxComponent } from './flux/flux.component';
import { ShakerComponent } from './shaker/shaker.component';
import { IrisFormComponent } from './iris-form/iris-form.component';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { FooterComponent } from './shared/footer/footer.component';
import { PersonalityTestComponent } from './personality-test/personality-test.component';
import { AdminComponent } from './admin/admin.component';

@NgModule({
  declarations: [
    AppComponent,
    AccueilComponent,
    SuivantaccComponent,
    TypeirisComponent,
    Iris2Component,
    FleurComponent,
    BijouComponent,
    FluxComponent,
    ShakerComponent,
    IrisFormComponent,
    LoginComponent,
    SignupComponent,
    IrisDiversityComponent,
    DashboardComponent,
    FooterComponent,
    PersonalityTestComponent,
    AdminComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    provideFirebaseApp(() => initializeApp(firebaseConfig)),
    provideDatabase(() => getDatabase())
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
