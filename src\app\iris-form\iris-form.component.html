<div class="iris-form-container">
  <div class="container">
    <div class="form-card">
      <div class="form-header">
        <h2 class="title">Analysez votre iris</h2>
        <p class="subtitle">Découvrez votre type d'iris grâce à notre IA</p>
        <div class="divider"></div>
      </div>

      <div class="form-content">
        <div class="upload-section">
          <div class="upload-container"
               [ngClass]="{'has-image': previewUrl}"
               (click)="fileInput.click()"
               (dragover)="onDragOver($event)"
               (dragleave)="onDragLeave($event)"
               (drop)="onDrop($event)">

            <input type="file"
                   #fileInput
                   (change)="onFileSelected($event)"
                   accept="image/*"
                   style="display: none;">

            <div class="upload-placeholder" *ngIf="!previewUrl">
              <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
              </div>
              <p class="upload-text">Glissez-déposez une photo de votre iris ici</p>
              <p class="upload-text-small">ou cliquez pour sélectionner un fichier</p>
            </div>

            <div class="preview-container" *ngIf="previewUrl">
              <img [src]="previewUrl" alt="Aperçu de l'iris" class="preview-image">
              <button class="remove-btn" (click)="removeImage($event)">×</button>
            </div>
          </div>

          <div class="upload-instructions">
            <h3>Instructions pour une bonne photo :</h3>
            <ul>
              <li>Prenez une photo claire et nette de votre iris</li>
              <li>Assurez-vous d'avoir un bon éclairage</li>
              <li>Évitez les reflets dans l'œil</li>
              <li>Cadrez bien l'iris pour qu'il soit visible en entier</li>
            </ul>
          </div>
        </div>

        <div class="form-fields">
          <div class="form-group">
            <label for="name">Nom</label>
            <input type="text" id="name" [(ngModel)]="formData.name" placeholder="Votre nom">
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" [(ngModel)]="formData.email" placeholder="Votre email">
          </div>

          <div class="form-group">
            <label for="age">Âge</label>
            <input type="number" id="age" [(ngModel)]="formData.age" placeholder="Votre âge">
          </div>

          <div class="form-group">
            <label>Genre</label>
            <div class="radio-group">
              <label>
                <input type="radio" name="gender" value="male" [(ngModel)]="formData.gender">
                Homme
              </label>
              <label>
                <input type="radio" name="gender" value="female" [(ngModel)]="formData.gender">
                Femme
              </label>
              <label>
                <input type="radio" name="gender" value="other" [(ngModel)]="formData.gender">
                Autre
              </label>
            </div>
          </div>

          <div class="form-group">
            <label for="comments">Commentaires supplémentaires</label>
            <textarea id="comments" [(ngModel)]="formData.comments" placeholder="Partagez vos observations ou questions..."></textarea>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button class="submit-btn" (click)="analyzeIris()" [disabled]="!previewUrl">
          <span *ngIf="!isAnalyzing">Analyser mon iris</span>
          <span *ngIf="isAnalyzing">Analyse en cours...</span>
        </button>
      </div>

      <div class="result-section" *ngIf="analysisResult">
        <div class="result-card">
          <h3 class="result-title">Résultat de l'analyse</h3>
          <div class="divider"></div>

          <div class="result-content">
            <div class="result-type">
              <h4>Votre type d'iris principal :</h4>
              <p class="type-name">{{ analysisResult.primaryType }}</p>
            </div>

            <div class="result-chart">
              <div class="chart-bar">
                <span class="bar-label">Fleur</span>
                <div class="bar-container">
                  <div class="bar-fill fleur" [style.width.%]="analysisResult.fleurPercentage"></div>
                </div>
                <span class="bar-value">{{ analysisResult.fleurPercentage }}%</span>
              </div>

              <div class="chart-bar">
                <span class="bar-label">Bijou</span>
                <div class="bar-container">
                  <div class="bar-fill bijou" [style.width.%]="analysisResult.bijouPercentage"></div>
                </div>
                <span class="bar-value">{{ analysisResult.bijouPercentage }}%</span>
              </div>

              <div class="chart-bar">
                <span class="bar-label">Flux</span>
                <div class="bar-container">
                  <div class="bar-fill flux" [style.width.%]="analysisResult.fluxPercentage"></div>
                </div>
                <span class="bar-value">{{ analysisResult.fluxPercentage }}%</span>
              </div>

              <div class="chart-bar">
                <span class="bar-label">Shaker</span>
                <div class="bar-container">
                  <div class="bar-fill shaker" [style.width.%]="analysisResult.shakerPercentage"></div>
                </div>
                <span class="bar-value">{{ analysisResult.shakerPercentage }}%</span>
              </div>
            </div>

            <div class="result-description">
              <p>{{ analysisResult.description }}</p>
            </div>

            <div class="result-actions">
              <a [routerLink]="['/'+analysisResult.primaryTypeRoute]" class="learn-more-btn">
                En savoir plus sur ce type
              </a>
              <button class="share-btn">
                Partager mes résultats
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
