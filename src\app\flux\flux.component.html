<div class="iris-profile flux">
  <div class="container">
    <div class="header">
      <h1 class="title">Flux - Le Sensitif</h1>
      <p class="subtitle">Type intuitif, physique et empathique</p>
    </div>

    <div class="content">
      <div class="image-container">
        <img src="assets/3.png" alt="Flux" class="iris-image" />
        <div class="image-decoration"></div>
      </div>

      <div class="description">
        <div class="card traits-card">
          <h2>Caractéristiques</h2>
          <ul class="traits-list">
            <li>
              <span class="icon">🌿</span>
              <span class="text">Type intuitif, physique et empathique par nature</span>
            </li>
            <li>
              <span class="icon">👐</span>
              <span class="text">Intègre la vie via l'expérience sensorielle et corporelle</span>
            </li>
            <li>
              <span class="icon">🏃</span>
              <span class="text">Apprentissage kinesthésique : bouger, pratiquer, ressentir</span>
            </li>
            <li>
              <span class="icon">🧘</span>
              <span class="text">Calme, posé, attentionné, équilibre les autres</span>
            </li>
            <li>
              <span class="icon">🤲</span>
              <span class="text">Adapté aux soins, sport, et services humains</span>
            </li>
            <li>
              <span class="icon">👋</span>
              <span class="text">Communication physique : posture, gestes, toucher contrôlé</span>
            </li>
            <li>
              <span class="icon">🏔️</span>
              <span class="text">Apporte stabilité, empathie et soutien</span>
            </li>
            <li>
              <span class="icon">🌊</span>
              <span class="text">Peut se sentir impuissant ou débordé</span>
            </li>
            <li>
              <span class="icon">🌱</span>
              <span class="text">Leçon de vie : faire confiance, lâcher prise et trouver sa mission</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="navigation">
      <a routerLink="/iris2" class="btn back-btn">
        <span class="icon">←</span>
        <span>Retour aux types d'iris</span>
      </a>
    </div>
  </div>
</div>

<!-- Formulaire d'analyse d'iris -->
<app-iris-form></app-iris-form>
