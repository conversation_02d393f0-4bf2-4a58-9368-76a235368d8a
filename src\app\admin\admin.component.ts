import { Component, OnInit } from '@angular/core';
import { FirebaseDataInitService, QuestionFamily } from '../services/firebase-data-init.service';
import { PersonalityTestService } from '../services/personality-test.service';
import { Database, ref, get } from '@angular/fire/database';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.css']
})
export class AdminComponent implements OnInit {
  families: QuestionFamily[] = [];
  questions: any[] = [];
  userResponses: any[] = [];
  sessionStats: any[] = [];
  personalityTests: any[] = [];
  isLoading = false;
  selectedTab = 'families';

  constructor(
    private firebaseDataInitService: FirebaseDataInitService,
    private personalityTestService: PersonalityTestService,
    private database: Database
  ) {}

  ngOnInit(): void {
    this.loadAllData();
  }

  /**
   * Charge toutes les données depuis Firebase
   */
  loadAllData(): void {
    this.isLoading = true;
    
    // Charger les familles
    this.firebaseDataInitService.getPersonalityFamilies().subscribe({
      next: (families) => {
        this.families = families;
        console.log('✅ Familles chargées:', families.length);
      },
      error: (error) => {
        console.error('❌ Erreur chargement familles:', error);
      }
    });

    // Charger les questions
    this.firebaseDataInitService.getAllQuestions().subscribe({
      next: (questions) => {
        this.questions = questions;
        console.log('✅ Questions chargées:', questions.length);
      },
      error: (error) => {
        console.error('❌ Erreur chargement questions:', error);
      }
    });

    // Charger les réponses utilisateur
    this.loadUserResponses();

    // Charger les statistiques de session
    this.loadSessionStats();

    // Charger les tests de personnalité
    this.loadPersonalityTests();

    this.isLoading = false;
  }

  /**
   * Charge les réponses utilisateur
   */
  private loadUserResponses(): void {
    const responsesRef = ref(this.database, 'user_responses');
    get(responsesRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        this.userResponses = this.flattenUserResponses(data);
        console.log('✅ Réponses utilisateur chargées:', this.userResponses.length);
      }
    }).catch(error => {
      console.error('❌ Erreur chargement réponses:', error);
    });
  }

  /**
   * Charge les statistiques de session
   */
  private loadSessionStats(): void {
    const statsRef = ref(this.database, 'session_stats');
    get(statsRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        this.sessionStats = Object.keys(data).map(key => ({
          sessionId: key,
          ...data[key]
        }));
        console.log('✅ Statistiques de session chargées:', this.sessionStats.length);
      }
    }).catch(error => {
      console.error('❌ Erreur chargement stats:', error);
    });
  }

  /**
   * Charge les tests de personnalité
   */
  private loadPersonalityTests(): void {
    const testsRef = ref(this.database, 'personality_tests');
    get(testsRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        this.personalityTests = Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        }));
        console.log('✅ Tests de personnalité chargés:', this.personalityTests.length);
      }
    }).catch(error => {
      console.error('❌ Erreur chargement tests:', error);
    });
  }

  /**
   * Aplatit les réponses utilisateur pour l'affichage
   */
  private flattenUserResponses(data: any): any[] {
    const responses: any[] = [];
    
    Object.keys(data).forEach(userId => {
      Object.keys(data[userId]).forEach(sessionId => {
        Object.keys(data[userId][sessionId]).forEach(questionId => {
          responses.push({
            userId,
            sessionId,
            questionId,
            ...data[userId][sessionId][questionId]
          });
        });
      });
    });
    
    return responses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Initialise les données de base
   */
  initializeData(): void {
    this.isLoading = true;
    this.firebaseDataInitService.initializeBaseData().subscribe({
      next: (success) => {
        if (success) {
          console.log('✅ Données initialisées avec succès');
          this.loadAllData();
        } else {
          console.error('❌ Erreur lors de l\'initialisation');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Erreur initialisation:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Change l'onglet actif
   */
  selectTab(tab: string): void {
    this.selectedTab = tab;
  }

  /**
   * Formate une date pour l'affichage
   */
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('fr-FR');
  }

  /**
   * Obtient le nombre total de questions par famille
   */
  getTotalQuestionsByFamily(familyId: string): number {
    const family = this.families.find(f => f.id === familyId);
    return family ? family.questions.length : 0;
  }

  /**
   * Obtient les réponses pour un utilisateur spécifique
   */
  getResponsesForUser(userId: string): any[] {
    return this.userResponses.filter(response => response.userId === userId);
  }

  /**
   * Obtient les statistiques pour une session spécifique
   */
  getStatsForSession(sessionId: string): any {
    return this.sessionStats.find(stat => stat.sessionId === sessionId);
  }

  /**
   * Exporte les données en JSON
   */
  exportData(): void {
    const exportData = {
      families: this.families,
      questions: this.questions,
      userResponses: this.userResponses,
      sessionStats: this.sessionStats,
      personalityTests: this.personalityTests,
      exportedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `personality-test-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }
}
