import { Component, OnInit } from '@angular/core';
import { FirebaseDataInitService, QuestionFamily } from '../services/firebase-data-init.service';
import { PersonalityTestService } from '../services/personality-test.service';
import { Firestore, collection, getDocs } from '@angular/fire/firestore';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.css']
})
export class AdminComponent implements OnInit {
  families: QuestionFamily[] = [];
  questions: any[] = [];
  userResponses: any[] = [];
  sessionStats: any[] = [];
  personalityTests: any[] = [];
  isLoading = false;
  selectedTab = 'families';

  constructor(
    private firebaseDataInitService: FirebaseDataInitService,
    private personalityTestService: PersonalityTestService,
    private firestore: Firestore
  ) {}

  ngOnInit(): void {
    this.loadAllData();
  }

  /**
   * Charge toutes les données depuis Firebase
   */
  loadAllData(): void {
    this.isLoading = true;

    // Charger les familles
    this.firebaseDataInitService.getPersonalityFamilies().subscribe({
      next: (families) => {
        this.families = families;
        console.log('✅ Familles chargées:', families.length);
      },
      error: (error) => {
        console.error('❌ Erreur chargement familles:', error);
      }
    });

    // Charger les questions
    this.firebaseDataInitService.getAllQuestions().subscribe({
      next: (questions) => {
        this.questions = questions;
        console.log('✅ Questions chargées:', questions.length);
      },
      error: (error) => {
        console.error('❌ Erreur chargement questions:', error);
      }
    });

    // Charger les réponses utilisateur
    this.loadUserResponses();

    // Charger les statistiques de session
    this.loadSessionStats();

    // Charger les tests de personnalité
    this.loadPersonalityTests();

    this.isLoading = false;
  }

  /**
   * Charge les réponses utilisateur
   */
  private loadUserResponses(): void {
    const responsesCollection = collection(this.firestore, 'user_responses');
    getDocs(responsesCollection).then(snapshot => {
      this.userResponses = [];
      snapshot.forEach(doc => {
        this.userResponses.push({
          id: doc.id,
          ...doc.data()
        });
      });
      this.userResponses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      console.log('✅ Réponses utilisateur chargées:', this.userResponses.length);
    }).catch(error => {
      console.error('❌ Erreur chargement réponses:', error);
    });
  }

  /**
   * Charge les statistiques de session
   */
  private loadSessionStats(): void {
    const statsCollection = collection(this.firestore, 'session_stats');
    getDocs(statsCollection).then(snapshot => {
      this.sessionStats = [];
      snapshot.forEach(doc => {
        this.sessionStats.push({
          sessionId: doc.id,
          ...doc.data()
        });
      });
      console.log('✅ Statistiques de session chargées:', this.sessionStats.length);
    }).catch(error => {
      console.error('❌ Erreur chargement stats:', error);
    });
  }

  /**
   * Charge les tests de personnalité
   */
  private loadPersonalityTests(): void {
    const testsCollection = collection(this.firestore, 'personality_tests');
    getDocs(testsCollection).then(snapshot => {
      this.personalityTests = [];
      snapshot.forEach(doc => {
        this.personalityTests.push({
          id: doc.id,
          ...doc.data()
        });
      });
      console.log('✅ Tests de personnalité chargés:', this.personalityTests.length);
    }).catch(error => {
      console.error('❌ Erreur chargement tests:', error);
    });
  }



  /**
   * Initialise les données de base
   */
  initializeData(): void {
    this.isLoading = true;
    this.firebaseDataInitService.initializeBaseData().subscribe({
      next: (success) => {
        if (success) {
          console.log('✅ Données initialisées avec succès');
          this.loadAllData();
        } else {
          console.error('❌ Erreur lors de l\'initialisation');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Erreur initialisation:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Change l'onglet actif
   */
  selectTab(tab: string): void {
    this.selectedTab = tab;
  }

  /**
   * Formate une date pour l'affichage
   */
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('fr-FR');
  }

  /**
   * Obtient le nombre total de questions par famille
   */
  getTotalQuestionsByFamily(familyId: string): number {
    const family = this.families.find(f => f.id === familyId);
    return family ? family.questions.length : 0;
  }

  /**
   * Obtient les réponses pour un utilisateur spécifique
   */
  getResponsesForUser(userId: string): any[] {
    return this.userResponses.filter(response => response.userId === userId);
  }

  /**
   * Obtient les statistiques pour une session spécifique
   */
  getStatsForSession(sessionId: string): any {
    return this.sessionStats.find(stat => stat.sessionId === sessionId);
  }

  /**
   * Exporte les données en JSON
   */
  exportData(): void {
    const exportData = {
      families: this.families,
      questions: this.questions,
      userResponses: this.userResponses,
      sessionStats: this.sessionStats,
      personalityTests: this.personalityTests,
      exportedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `personality-test-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    URL.revokeObjectURL(url);
  }
}
