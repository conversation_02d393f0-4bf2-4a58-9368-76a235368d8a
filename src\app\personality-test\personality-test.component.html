<div class="personality-test-container">
  <!-- Page d'accueil du test -->
  <div class="test-intro" *ngIf="!isTestStarted && !showResults">
    <div class="intro-card">
      <div class="intro-header">
        <h1 class="title">Test de Personnalité</h1>
        <div class="divider"></div>
        <p class="subtitle">Découvrez votre profil psychotechnique</p>
      </div>

      <div class="intro-content">
        <div class="test-info">
          <div class="info-item">
            <span class="icon">📝</span>
            <div class="info-text">
              <h3>32 Questions</h3>
              <p>Questions ciblées pour analyser votre personnalité</p>
            </div>
          </div>

          <div class="info-item">
            <span class="icon">⏱️</span>
            <div class="info-text">
              <h3>5-10 Minutes</h3>
              <p>Temps estimé pour compléter le test</p>
            </div>
          </div>

          <div class="info-item">
            <span class="icon">🎯</span>
            <div class="info-text">
              <h3>4 Profils Principaux</h3>
              <p>Flower, Jewel, Shaker, Stream + profils intermédiaires</p>
            </div>
          </div>
        </div>

        <div class="user-info">
          <h3>Informations du test</h3>
          <p><strong>Nom:</strong> {{ selectedUser.name }}</p>
          <p><strong>Email:</strong> {{ selectedUser.email }}</p>
          <p class="note">Les résultats seront sauvegardés dans la base de données PFA1</p>

          <div class="user-actions" *ngIf="currentUser">
            <button class="btn btn-logout" (click)="logout()">
              <span>Se déconnecter</span>
              <span class="icon">🚪</span>
            </button>
          </div>
        </div>
      </div>

      <div class="intro-actions">
        <button class="btn btn-primary" (click)="startTest()">
          <span>Commencer le Test</span>
          <span class="icon">→</span>
        </button>
        <button class="btn btn-secondary" (click)="goToHome()">
          Retour à l'accueil
        </button>
      </div>
    </div>
  </div>

  <!-- Interface du test -->
  <div class="test-interface" *ngIf="isTestStarted && !showResults">
    <div class="test-card">
      <!-- Barre de progression -->
      <div class="progress-section">
        <div class="progress-info">
          <span class="question-counter">Question {{ currentQuestionNumber }} sur {{ totalQuestions }}</span>
          <span class="progress-percentage">{{ progressPercentage | number:'1.0-0' }}%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="progressPercentage"></div>
        </div>
      </div>

      <!-- Question actuelle -->
      <div class="question-section" *ngIf="currentQuestion">
        <div class="question-content">
          <h2 class="question-text">{{ currentQuestion.question }}</h2>
        </div>

        <div class="answer-buttons">
          <button
            class="btn btn-answer btn-yes"
            (click)="answerQuestion(true)"
            [disabled]="isLoading">
            <span class="answer-icon">✓</span>
            <span>Oui</span>
          </button>

          <button
            class="btn btn-answer btn-no"
            (click)="answerQuestion(false)"
            [disabled]="isLoading">
            <span class="answer-icon">✗</span>
            <span>Non</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Écran de chargement -->
  <div class="loading-screen" *ngIf="isLoading">
    <div class="loading-card">
      <div class="loading-spinner"></div>
      <h2>Analyse de votre profil...</h2>
      <p>Calcul des scores et détermination de votre personnalité</p>
    </div>
  </div>

  <!-- Résultats du test -->
  <div class="test-results" *ngIf="showResults && finalProfile">
    <div class="results-card">
      <div class="results-header">
        <h1 class="title">Votre Profil de Personnalité</h1>
        <div class="divider"></div>
      </div>

      <div class="profile-summary">
        <div class="profile-badge" [class]="'profile-' + finalProfile.primaryClass.toLowerCase().replace('-', '')">
          <h2 class="profile-name">{{ finalProfile.primaryClass }}</h2>
          <div class="confidence-score">
            <span class="score-label">Score de confiance</span>
            <span class="score-value">{{ finalProfile.confidenceScore }}%</span>
          </div>
        </div>

        <div class="profile-type" *ngIf="finalProfile.isIntermediate">
          <span class="type-badge intermediate">Profil Intermédiaire</span>
        </div>
        <div class="profile-type" *ngIf="!finalProfile.isIntermediate">
          <span class="type-badge primary">Profil Principal</span>
        </div>
      </div>

      <div class="profile-description">
        <h3>Description de votre profil</h3>
        <p>{{ finalProfile.description }}</p>
      </div>

      <!-- Compatibilité avec l'iris -->
      <div class="iris-compatibility" *ngIf="compatibilityResult">
        <h3>Compatibilité avec votre Iris</h3>

        <div class="compatibility-summary" [class]="compatibilityResult.isCompatible ? 'compatible' : 'incompatible'">
          <div class="compatibility-header">
            <div class="iris-info">
              <h4>{{ compatibilityResult.irisType.name }}</h4>
              <p>{{ compatibilityResult.irisType.description }}</p>
            </div>
            <div class="compatibility-score">
              <div class="score-circle" [class]="compatibilityResult.isCompatible ? 'high-score' : 'low-score'">
                <span class="score-value">{{ compatibilityResult.compatibilityScore }}%</span>
                <span class="score-label">Compatibilité</span>
              </div>
            </div>
          </div>

          <div class="compatibility-status">
            <span class="status-badge" [class]="compatibilityResult.isCompatible ? 'compatible' : 'incompatible'">
              {{ compatibilityResult.isCompatible ? '✓ Compatible' : '⚠ Incompatible' }}
            </span>
          </div>
        </div>

        <div class="iris-characteristics">
          <h4>Caractéristiques de votre iris</h4>
          <div class="characteristics-list">
            <span *ngFor="let characteristic of compatibilityResult.irisType.characteristics"
                  class="characteristic-tag">
              {{ characteristic }}
            </span>
          </div>
        </div>

        <div class="recommendations">
          <h4>Recommandations</h4>
          <ul class="recommendations-list">
            <li *ngFor="let recommendation of compatibilityResult.recommendations">
              {{ recommendation }}
            </li>
          </ul>
        </div>

        <div class="corrections" *ngIf="!compatibilityResult.isCompatible">
          <h4>Corrections suggérées</h4>
          <ul class="corrections-list">
            <li *ngFor="let correction of compatibilityResult.corrections">
              {{ correction }}
            </li>
          </ul>
        </div>
      </div>

      <div class="detailed-scores" *ngIf="testSession">
        <h3>Scores détaillés</h3>
        <div class="scores-grid">
          <div class="score-item">
            <span class="score-label">Flower</span>
            <div class="score-bar">
              <div class="score-fill flower" [style.width.%]="(testSession.scores.flower / 4) * 100"></div>
            </div>
            <span class="score-value">{{ testSession.scores.flower }}/4</span>
          </div>

          <div class="score-item">
            <span class="score-label">Jewel</span>
            <div class="score-bar">
              <div class="score-fill jewel" [style.width.%]="(testSession.scores.jewel / 4) * 100"></div>
            </div>
            <span class="score-value">{{ testSession.scores.jewel }}/4</span>
          </div>

          <div class="score-item">
            <span class="score-label">Shaker</span>
            <div class="score-bar">
              <div class="score-fill shaker" [style.width.%]="(testSession.scores.shaker / 4) * 100"></div>
            </div>
            <span class="score-value">{{ testSession.scores.shaker }}/4</span>
          </div>

          <div class="score-item">
            <span class="score-label">Stream</span>
            <div class="score-bar">
              <div class="score-fill stream" [style.width.%]="(testSession.scores.stream / 4) * 100"></div>
            </div>
            <span class="score-value">{{ testSession.scores.stream }}/4</span>
          </div>
        </div>
      </div>

      <div class="test-info-summary">
        <div class="info-row">
          <span class="label">Test complété le:</span>
          <span class="value">{{ testSession?.completedAt | date:'dd/MM/yyyy à HH:mm' }}</span>
        </div>
        <div class="info-row" *ngIf="testSession?.id">
          <span class="label">ID de session:</span>
          <span class="value">{{ testSession?.id }}</span>
        </div>
      </div>

      <div class="results-actions">
        <button class="btn btn-primary" (click)="restartTest()">
          <span>Refaire le Test</span>
          <span class="icon">🔄</span>
        </button>
        <button class="btn btn-secondary" (click)="goToHome()">
          Retour à l'accueil
        </button>
        <button class="btn btn-logout" (click)="logout()" *ngIf="currentUser">
          <span>Se déconnecter</span>
          <span class="icon">🚪</span>
        </button>
      </div>
    </div>
  </div>
</div>
