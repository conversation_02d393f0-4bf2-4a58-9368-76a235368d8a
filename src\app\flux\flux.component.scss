// Styles pour le composant Flux
.iris-profile.flux {
  min-height: 100vh;
  background: var(--flux-gradient);
  padding: 40px 0;

  .header {
    text-align: center;
    margin-bottom: 40px;

    .title {
      color: var(--flux-primary);
      font-size: 2.5rem;
      margin-bottom: 10px;
      position: relative;
      display: inline-block;

      &:after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: var(--flux-secondary);
        border-radius: 3px;
      }
    }

    .subtitle {
      color: #666;
      font-size: 1.2rem;
      font-weight: 300;
      max-width: 600px;
      margin: 0 auto;
      margin-top: 20px;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    margin-bottom: 40px;

    .image-container {
      position: relative;

      .iris-image {
        width: 200px;
        height: 200px;
        object-fit: cover;
        border-radius: 50%;
        box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
        border: 5px solid white;
        z-index: 2;
        position: relative;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
        }
      }

      .image-decoration {
        position: absolute;
        width: 220px;
        height: 220px;
        border-radius: 50%;
        border: 2px dashed var(--flux-secondary);
        top: -10px;
        left: -10px;
        z-index: 1;
        animation: rotate 20s linear infinite;
      }
    }

    .description {
      width: 100%;
      max-width: 800px;

      .traits-card {
        padding: 30px;

        h2 {
          color: var(--flux-primary);
          font-size: 1.8rem;
          margin-bottom: 25px;
          text-align: center;
        }

        .traits-list {
          list-style: none;
          padding: 0;
          display: grid;
          grid-template-columns: 1fr;
          gap: 15px;

          li {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.7);
            transition: all 0.3s ease;

            &:hover {
              background-color: white;
              transform: translateX(5px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            }

            .icon {
              font-size: 1.5rem;
              margin-right: 15px;
              min-width: 30px;
              text-align: center;
            }

            .text {
              font-size: 1.1rem;
              color: #444;
            }
          }
        }
      }
    }
  }

  .navigation {
    display: flex;
    justify-content: center;
    margin-top: 30px;

    .back-btn {
      background-color: var(--flux-primary);
      color: white;
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 25px;
      border-radius: 50px;
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
      transition: all 0.3s ease;

      &:hover {
        background-color: darken(#3498db, 10%);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
      }

      .icon {
        font-size: 1.2rem;
      }
    }
  }
}

// Animation pour la décoration de l'image
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Media queries pour la responsivité
@media (min-width: 768px) {
  .iris-profile.flux {
    .content {
      flex-direction: row;
      align-items: flex-start;

      .image-container {
        flex: 0 0 auto;
      }

      .description {
        flex: 1;

        .traits-card {
          .traits-list {
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
    }
  }
}
