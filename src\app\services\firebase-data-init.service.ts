import { Injectable } from '@angular/core';
import { Database, ref, set, get } from '@angular/fire/database';
import { Observable, from } from 'rxjs';
import { PERSONALITY_QUESTIONS, PERSONALITY_DESCRIPTIONS } from '../models/personality-test.model';

export interface QuestionFamily {
  id: string;
  name: string;
  description: string;
  characteristics: string[];
  questions: {
    id: number;
    question: string;
    expectedAnswer: boolean;
    weight: number;
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseDataInitService {

  constructor(private database: Database) {}

  /**
   * Initialise toutes les données de base dans Firebase
   */
  initializeBaseData(): Observable<boolean> {
    return from(this.setupAllData());
  }

  private async setupAllData(): Promise<boolean> {
    try {
      // Vérifier si les données existent déjà
      const questionsRef = ref(this.database, 'questions');
      const questionsSnapshot = await get(questionsRef);
      
      if (!questionsSnapshot.exists()) {
        console.log('Initialisation des données de base...');
        
        // Initialiser les familles de personnalité
        await this.initializePersonalityFamilies();
        
        // Initialiser les questions
        await this.initializeQuestions();
        
        // Initialiser les descriptions
        await this.initializeDescriptions();
        
        console.log('Données de base initialisées avec succès !');
      } else {
        console.log('Données de base déjà présentes dans Firebase');
      }
      
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'initialisation des données:', error);
      return false;
    }
  }

  /**
   * Initialise les familles de personnalité
   */
  private async initializePersonalityFamilies(): Promise<void> {
    const families: QuestionFamily[] = [
      {
        id: 'flower',
        name: 'Flower',
        description: 'Personnalité émotionnelle, créative et empathique',
        characteristics: ['Émotionnel', 'Créatif', 'Empathique', 'Intuitif'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Flower')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'jewel',
        name: 'Jewel',
        description: 'Personnalité structurée, analytique et méthodique',
        characteristics: ['Structuré', 'Analytique', 'Méthodique', 'Logique'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Jewel')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'shaker',
        name: 'Shaker',
        description: 'Personnalité dynamique, aventurière et spontanée',
        characteristics: ['Dynamique', 'Aventurier', 'Spontané', 'Énergique'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Shaker')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'stream',
        name: 'Stream',
        description: 'Personnalité paisible, réfléchie et diplomate',
        characteristics: ['Paisible', 'Réfléchi', 'Diplomate', 'Calme'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Stream')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'flower-jewel',
        name: 'Flower-Jewel',
        description: 'Personnalité combinant sensibilité et organisation',
        characteristics: ['Sensible', 'Organisé', 'Créatif-Méthodique', 'Équilibré'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Flower-Jewel')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'jewel-shaker',
        name: 'Jewel-Shaker',
        description: 'Personnalité alliant méthode et dynamisme',
        characteristics: ['Méthodique', 'Énergique', 'Analytique-Actif', 'Efficace'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Jewel-Shaker')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'shaker-stream',
        name: 'Shaker-Stream',
        description: 'Personnalité équilibrant action et réflexion',
        characteristics: ['Adaptable', 'Dynamique-Calme', 'Flexible', 'Réactif'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Shaker-Stream')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'stream-flower',
        name: 'Stream-Flower',
        description: 'Personnalité mêlant paix et émotion',
        characteristics: ['Paisible', 'Émotionnel', 'Intuitif-Calme', 'Empathique'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Stream-Flower')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      }
    ];

    const familiesRef = ref(this.database, 'personality_families');
    await set(familiesRef, families.reduce((acc, family) => {
      acc[family.id] = family;
      return acc;
    }, {} as any));
  }

  /**
   * Initialise toutes les questions
   */
  private async initializeQuestions(): Promise<void> {
    const questionsData = PERSONALITY_QUESTIONS.map(q => ({
      id: q.id,
      question: q.question,
      expectedAnswer: q.expectedAnswer,
      classes: q.classes,
      weight: q.weight || 1,
      category: this.getQuestionCategory(q.id),
      createdAt: new Date().toISOString()
    }));

    const questionsRef = ref(this.database, 'questions');
    await set(questionsRef, questionsData.reduce((acc, question) => {
      acc[question.id] = question;
      return acc;
    }, {} as any));
  }

  /**
   * Initialise les descriptions des profils
   */
  private async initializeDescriptions(): Promise<void> {
    const descriptionsRef = ref(this.database, 'personality_descriptions');
    await set(descriptionsRef, PERSONALITY_DESCRIPTIONS);
  }

  /**
   * Détermine la catégorie d'une question selon son ID
   */
  private getQuestionCategory(questionId: number): string {
    if (questionId >= 1 && questionId <= 4) return 'flower';
    if (questionId >= 5 && questionId <= 8) return 'jewel';
    if (questionId >= 9 && questionId <= 12) return 'shaker';
    if (questionId >= 13 && questionId <= 16) return 'stream';
    if (questionId >= 17 && questionId <= 19) return 'flower-jewel';
    if (questionId >= 20 && questionId <= 22) return 'jewel-shaker';
    if (questionId >= 23 && questionId <= 25) return 'shaker-stream';
    if (questionId >= 26 && questionId <= 28) return 'stream-flower';
    if (questionId >= 29 && questionId <= 32) return 'mixed';
    return 'unknown';
  }

  /**
   * Sauvegarde une réponse utilisateur détaillée
   */
  saveUserResponse(userId: string, sessionId: string, questionId: number, answer: boolean, responseTime?: number): Observable<boolean> {
    const responseData = {
      userId,
      sessionId,
      questionId,
      answer,
      responseTime: responseTime || 0,
      timestamp: new Date().toISOString()
    };

    const responseRef = ref(this.database, `user_responses/${userId}/${sessionId}/${questionId}`);
    return from(set(responseRef, responseData).then(() => true).catch(() => false));
  }

  /**
   * Récupère toutes les familles de personnalité
   */
  getPersonalityFamilies(): Observable<QuestionFamily[]> {
    const familiesRef = ref(this.database, 'personality_families');
    return from(get(familiesRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        return Object.values(data) as QuestionFamily[];
      }
      return [];
    }));
  }

  /**
   * Récupère toutes les questions
   */
  getAllQuestions(): Observable<any[]> {
    const questionsRef = ref(this.database, 'questions');
    return from(get(questionsRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        return Object.values(data);
      }
      return [];
    }));
  }
}
