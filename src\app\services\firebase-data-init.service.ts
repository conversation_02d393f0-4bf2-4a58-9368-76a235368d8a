import { Injectable } from '@angular/core';
import { Firestore, collection, doc, setDoc, getDoc, getDocs, addDoc } from '@angular/fire/firestore';
import { Observable, from } from 'rxjs';
import { PERSONALITY_QUESTIONS, PERSONALITY_DESCRIPTIONS } from '../models/personality-test.model';

export interface QuestionFamily {
  id: string;
  name: string;
  description: string;
  characteristics: string[];
  questions: {
    id: number;
    question: string;
    expectedAnswer: boolean;
    weight: number;
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseDataInitService {

  constructor(private firestore: Firestore) {}

  /**
   * Initialise toutes les données de base dans Firebase
   */
  initializeBaseData(): Observable<boolean> {
    return from(this.setupAllData());
  }

  private async setupAllData(): Promise<boolean> {
    try {
      // Vérifier si les données existent déjà
      const questionsCollection = collection(this.firestore, 'questions');
      const questionsSnapshot = await getDocs(questionsCollection);

      if (questionsSnapshot.empty) {
        console.log('Initialisation des données de base...');

        // Initialiser les familles de personnalité
        await this.initializePersonalityFamilies();

        // Initialiser les questions
        await this.initializeQuestions();

        // Initialiser les descriptions
        await this.initializeDescriptions();

        console.log('Données de base initialisées avec succès !');
      } else {
        console.log('Données de base déjà présentes dans Firebase');
      }

      return true;
    } catch (error) {
      console.error('Erreur lors de l\'initialisation des données:', error);
      return false;
    }
  }

  /**
   * Initialise les familles de personnalité
   */
  private async initializePersonalityFamilies(): Promise<void> {
    const families: QuestionFamily[] = [
      {
        id: 'flower',
        name: 'Flower',
        description: 'Personnalité émotionnelle, créative et empathique',
        characteristics: ['Émotionnel', 'Créatif', 'Empathique', 'Intuitif'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Flower')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'jewel',
        name: 'Jewel',
        description: 'Personnalité structurée, analytique et méthodique',
        characteristics: ['Structuré', 'Analytique', 'Méthodique', 'Logique'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Jewel')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'shaker',
        name: 'Shaker',
        description: 'Personnalité dynamique, aventurière et spontanée',
        characteristics: ['Dynamique', 'Aventurier', 'Spontané', 'Énergique'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Shaker')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'stream',
        name: 'Stream',
        description: 'Personnalité paisible, réfléchie et diplomate',
        characteristics: ['Paisible', 'Réfléchi', 'Diplomate', 'Calme'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Stream')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'flower-jewel',
        name: 'Flower-Jewel',
        description: 'Personnalité combinant sensibilité et organisation',
        characteristics: ['Sensible', 'Organisé', 'Créatif-Méthodique', 'Équilibré'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Flower-Jewel')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'jewel-shaker',
        name: 'Jewel-Shaker',
        description: 'Personnalité alliant méthode et dynamisme',
        characteristics: ['Méthodique', 'Énergique', 'Analytique-Actif', 'Efficace'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Jewel-Shaker')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'shaker-stream',
        name: 'Shaker-Stream',
        description: 'Personnalité équilibrant action et réflexion',
        characteristics: ['Adaptable', 'Dynamique-Calme', 'Flexible', 'Réactif'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Shaker-Stream')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      },
      {
        id: 'stream-flower',
        name: 'Stream-Flower',
        description: 'Personnalité mêlant paix et émotion',
        characteristics: ['Paisible', 'Émotionnel', 'Intuitif-Calme', 'Empathique'],
        questions: PERSONALITY_QUESTIONS.filter(q => q.classes.includes('Stream-Flower')).map(q => ({
          id: q.id,
          question: q.question,
          expectedAnswer: q.expectedAnswer,
          weight: q.weight || 1
        }))
      }
    ];

    // Sauvegarder chaque famille dans Firestore
    for (const family of families) {
      const familyDoc = doc(this.firestore, 'personality_families', family.id);
      await setDoc(familyDoc, family);
    }
  }

  /**
   * Initialise toutes les questions
   */
  private async initializeQuestions(): Promise<void> {
    const questionsData = PERSONALITY_QUESTIONS.map(q => ({
      id: q.id,
      question: q.question,
      expectedAnswer: q.expectedAnswer,
      classes: q.classes,
      weight: q.weight || 1,
      category: this.getQuestionCategory(q.id),
      createdAt: new Date().toISOString()
    }));

    // Sauvegarder chaque question dans Firestore
    for (const question of questionsData) {
      const questionDoc = doc(this.firestore, 'questions', question.id.toString());
      await setDoc(questionDoc, question);
    }
  }

  /**
   * Initialise les descriptions des profils
   */
  private async initializeDescriptions(): Promise<void> {
    // Sauvegarder chaque description dans Firestore
    for (const [key, description] of Object.entries(PERSONALITY_DESCRIPTIONS)) {
      const descriptionDoc = doc(this.firestore, 'personality_descriptions', key);
      await setDoc(descriptionDoc, { description });
    }
  }

  /**
   * Détermine la catégorie d'une question selon son ID
   */
  private getQuestionCategory(questionId: number): string {
    if (questionId >= 1 && questionId <= 4) return 'flower';
    if (questionId >= 5 && questionId <= 8) return 'jewel';
    if (questionId >= 9 && questionId <= 12) return 'shaker';
    if (questionId >= 13 && questionId <= 16) return 'stream';
    if (questionId >= 17 && questionId <= 19) return 'flower-jewel';
    if (questionId >= 20 && questionId <= 22) return 'jewel-shaker';
    if (questionId >= 23 && questionId <= 25) return 'shaker-stream';
    if (questionId >= 26 && questionId <= 28) return 'stream-flower';
    if (questionId >= 29 && questionId <= 32) return 'mixed';
    return 'unknown';
  }

  /**
   * Sauvegarde une réponse utilisateur détaillée
   */
  saveUserResponse(userId: string, sessionId: string, questionId: number, answer: boolean, responseTime?: number): Observable<boolean> {
    const responseData = {
      userId,
      sessionId,
      questionId,
      answer,
      responseTime: responseTime || 0,
      timestamp: new Date().toISOString()
    };

    const responseDoc = doc(this.firestore, 'user_responses', `${userId}_${sessionId}_${questionId}`);
    return from(setDoc(responseDoc, responseData).then(() => true).catch(() => false));
  }

  /**
   * Récupère toutes les familles de personnalité
   */
  getPersonalityFamilies(): Observable<QuestionFamily[]> {
    const familiesCollection = collection(this.firestore, 'personality_families');
    return from(getDocs(familiesCollection).then(snapshot => {
      const families: QuestionFamily[] = [];
      snapshot.forEach(doc => {
        families.push(doc.data() as QuestionFamily);
      });
      return families;
    }));
  }

  /**
   * Récupère toutes les questions
   */
  getAllQuestions(): Observable<any[]> {
    const questionsCollection = collection(this.firestore, 'questions');
    return from(getDocs(questionsCollection).then(snapshot => {
      const questions: any[] = [];
      snapshot.forEach(doc => {
        questions.push(doc.data());
      });
      return questions.sort((a, b) => a.id - b.id);
    }));
  }
}
