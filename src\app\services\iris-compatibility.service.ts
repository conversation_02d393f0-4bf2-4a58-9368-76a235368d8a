import { Injectable } from '@angular/core';
import { PersonalityClass, PersonalityProfile } from '../models/personality-test.model';
import { IrisImageService, IrisAnalysisResult } from './iris-image.service';

export interface IrisType {
  id: string;
  name: string;
  description: string;
  compatiblePersonalities: PersonalityClass[];
  characteristics: string[];
}

export interface CompatibilityResult {
  isCompatible: boolean;
  compatibilityScore: number;
  irisType: IrisType;
  personalityProfile: PersonalityProfile;
  recommendations: string[];
  corrections: string[];
}

@Injectable({
  providedIn: 'root'
})
export class IrisCompatibilityService {

  // Types d'iris avec leurs personnalités compatibles
  private irisTypes: IrisType[] = [
    {
      id: 'crypte-dominant',
      name: 'Crypte Dominant',
      description: 'Iris avec des cryptes prononcées, indique une personnalité analytique',
      compatiblePersonalities: ['Jewel', 'Jewel-Shaker', 'Flower-Jewel'],
      characteristics: ['Analytique', 'Méthodique', 'Logique', 'Structuré']
    },
    {
      id: 'lacunes-radiales',
      name: 'Lacunes Radiales',
      description: 'Iris avec des lacunes radiales, indique une personnalité créative',
      compatiblePersonalities: ['Flower', 'Stream-Flower', 'Flower-Jewel'],
      characteristics: ['Créatif', 'Émotionnel', 'Intuitif', 'Artistique']
    },
    {
      id: 'fibres-rayonnantes',
      name: 'Fibres Rayonnantes',
      description: 'Iris avec des fibres rayonnantes, indique une personnalité dynamique',
      compatiblePersonalities: ['Shaker', 'Jewel-Shaker', 'Shaker-Stream'],
      characteristics: ['Dynamique', 'Énergique', 'Aventurier', 'Spontané']
    },
    {
      id: 'texture-fine',
      name: 'Texture Fine',
      description: 'Iris avec une texture fine, indique une personnalité paisible',
      compatiblePersonalities: ['Stream', 'Stream-Flower', 'Shaker-Stream'],
      characteristics: ['Paisible', 'Réfléchi', 'Calme', 'Diplomate']
    },
    {
      id: 'mixte-complexe',
      name: 'Mixte Complexe',
      description: 'Iris avec plusieurs caractéristiques, personnalité équilibrée',
      compatiblePersonalities: ['Flower-Jewel', 'Jewel-Shaker', 'Shaker-Stream', 'Stream-Flower'],
      characteristics: ['Équilibré', 'Adaptable', 'Polyvalent', 'Complexe']
    }
  ];

  constructor(private irisImageService: IrisImageService) { }

  /**
   * Simule la détection du type d'iris (en réalité, cela viendrait d'une analyse d'image)
   */
  detectIrisType(userEmail: string): IrisType {
    // Simulation basée sur l'email pour les tests
    const hash = this.hashCode(userEmail);
    const index = Math.abs(hash) % this.irisTypes.length;
    return this.irisTypes[index];
  }

  /**
   * Vérifie la compatibilité entre l'iris et le profil de personnalité
   */
  checkCompatibility(personalityProfile: PersonalityProfile, userEmail: string): CompatibilityResult {
    // Vérifier si l'utilisateur a une analyse d'iris réelle
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    let irisType: IrisType;
    let compatibilityScore = 0;

    if (currentUser.irisAnalysis) {
      // Utiliser l'analyse réelle de l'iris
      irisType = this.mapAnalysisToIrisType(currentUser.irisAnalysis);
      compatibilityScore = currentUser.irisAnalysis.compatibilityScore;
    } else {
      // Utiliser la simulation basée sur l'email
      irisType = this.detectIrisType(userEmail);
      if (irisType.compatiblePersonalities.includes(personalityProfile.primaryClass)) {
        compatibilityScore = 85 + Math.random() * 15; // 85-100%
      } else {
        compatibilityScore = 30 + Math.random() * 40; // 30-70%
      }
    }

    const isCompatible = irisType.compatiblePersonalities.includes(personalityProfile.primaryClass);
    const recommendations = this.generateRecommendations(irisType, personalityProfile, isCompatible);
    const corrections = this.generateCorrections(irisType, personalityProfile, isCompatible);

    return {
      isCompatible,
      compatibilityScore: Math.round(compatibilityScore),
      irisType,
      personalityProfile,
      recommendations,
      corrections
    };
  }

  /**
   * Convertit une analyse d'iris en type d'iris
   */
  private mapAnalysisToIrisType(analysis: IrisAnalysisResult): IrisType {
    // Mapper le type détecté vers nos types d'iris
    const typeMapping: { [key: string]: string } = {
      'Flower': 'lacunes-radiales',
      'Jewel': 'crypte-dominant',
      'Shaker': 'fibres-rayonnantes',
      'Stream': 'texture-fine'
    };

    const mappedId = typeMapping[analysis.irisType] || 'mixte-complexe';
    const baseType = this.irisTypes.find(t => t.id === mappedId) || this.irisTypes[4];

    // Créer un type personnalisé basé sur l'analyse
    return {
      id: `analyzed-${analysis.irisType.toLowerCase()}`,
      name: `${analysis.irisType} (Analysé)`,
      description: `Iris analysé automatiquement - Type ${analysis.irisType}`,
      compatiblePersonalities: baseType.compatiblePersonalities,
      characteristics: analysis.characteristics
    };
  }

  /**
   * Génère des recommandations basées sur la compatibilité
   */
  private generateRecommendations(iris: IrisType, personality: PersonalityProfile, isCompatible: boolean): string[] {
    if (isCompatible) {
      return [
        `Votre iris ${iris.name} est parfaitement compatible avec votre profil ${personality.primaryClass}`,
        `Continuez à développer vos traits ${iris.characteristics.join(', ').toLowerCase()}`,
        `Votre authenticité naturelle est un atout majeur`,
        `Exploitez cette harmonie pour maximiser votre potentiel`
      ];
    } else {
      return [
        `Il y a une divergence entre votre iris ${iris.name} et votre profil ${personality.primaryClass}`,
        `Considérez développer davantage vos aspects ${iris.characteristics.join(', ').toLowerCase()}`,
        `Cette différence peut être une source de croissance personnelle`,
        `Travaillez sur l'équilibre entre vos tendances naturelles et acquises`
      ];
    }
  }

  /**
   * Génère des corrections suggérées
   */
  private generateCorrections(iris: IrisType, personality: PersonalityProfile, isCompatible: boolean): string[] {
    if (isCompatible) {
      return [
        'Aucune correction majeure nécessaire',
        'Continuez sur votre voie actuelle',
        'Affinez vos points forts existants'
      ];
    } else {
      const corrections = [];

      // Corrections spécifiques selon le type d'iris
      switch (iris.id) {
        case 'crypte-dominant':
          corrections.push(
            'Développez davantage votre côté analytique et méthodique',
            'Prenez plus de temps pour planifier vos actions',
            'Utilisez des outils de structuration (listes, tableaux, plans)'
          );
          break;
        case 'lacunes-radiales':
          corrections.push(
            'Explorez davantage votre créativité et votre intuition',
            'Accordez plus d\'importance à vos émotions',
            'Engagez-vous dans des activités artistiques ou créatives'
          );
          break;
        case 'fibres-rayonnantes':
          corrections.push(
            'Embrassez davantage l\'action et le dynamisme',
            'Sortez de votre zone de confort plus souvent',
            'Prenez des initiatives et soyez plus spontané'
          );
          break;
        case 'texture-fine':
          corrections.push(
            'Cultivez la patience et la réflexion',
            'Privilégiez les environnements calmes',
            'Développez vos capacités d\'écoute et de médiation'
          );
          break;
        default:
          corrections.push(
            'Travaillez sur l\'équilibre entre tous vos aspects',
            'Développez votre adaptabilité',
            'Explorez différentes facettes de votre personnalité'
          );
      }

      return corrections;
    }
  }

  /**
   * Fonction de hachage simple pour simuler la détection d'iris
   */
  private hashCode(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
  }

  /**
   * Obtient tous les types d'iris disponibles
   */
  getAllIrisTypes(): IrisType[] {
    return [...this.irisTypes];
  }

  /**
   * Obtient un type d'iris par son ID
   */
  getIrisTypeById(id: string): IrisType | undefined {
    return this.irisTypes.find(iris => iris.id === id);
  }
}
