// Modèles pour le test de personnalité

export interface Question {
  id: number;
  question: string;
  expectedAnswer: boolean;
  classes: PersonalityClass[];
  weight?: number; // Poids de la question (par défaut 1)
}

export interface UserResponse {
  questionId: number;
  answer: boolean;
  timestamp: Date;
}

export interface TestSession {
  id?: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  responses: UserResponse[];
  scores: PersonalityScores;
  finalProfile: PersonalityProfile;
  completedAt: Date;
  startedAt: Date;
}

export interface PersonalityScores {
  flower: number;
  jewel: number;
  shaker: number;
  stream: number;
  // Classes intermédiaires
  flowerJewel: number;
  jewelShaker: number;
  shakerStream: number;
  streamFlower: number;
}

export interface PersonalityProfile {
  primaryClass: PersonalityClass;
  secondaryClass?: PersonalityClass;
  isIntermediate: boolean;
  confidenceScore: number;
  description: string;
}

export type PersonalityClass = 
  | 'Flower' 
  | 'Jewel' 
  | 'Shaker' 
  | 'Stream' 
  | 'Flower-Jewel' 
  | 'Jewel-Shaker' 
  | 'Shaker-Stream' 
  | 'Stream-Flower';

export const PERSONALITY_DESCRIPTIONS: Record<PersonalityClass, string> = {
  'Flower': 'Vous êtes une personne émotionnelle, créative et empathique. Vous vous laissez guider par vos sentiments et appréciez les environnements artistiques.',
  'Jewel': 'Vous êtes structuré, analytique et méthodique. Vous préférez la logique à la spontanéité et aimez planifier vos actions.',
  'Shaker': 'Vous êtes dynamique, aventurier et spontané. Vous aimez l\'action, la nouveauté et vous vous lassez facilement de la routine.',
  'Stream': 'Vous êtes paisible, réfléchi et diplomate. Vous préférez la tranquillité aux conflits et prenez le temps avant d\'agir.',
  'Flower-Jewel': 'Vous combinez sensibilité et organisation. Vous êtes créatif mais aimez suivre une méthode structurée.',
  'Jewel-Shaker': 'Vous alliez méthode et dynamisme. Vous êtes à la fois analytique et énergique, agissant vite mais de façon calculée.',
  'Shaker-Stream': 'Vous équilibrez action et réflexion. Vous êtes dynamique sans être impulsif, adaptable et calme.',
  'Stream-Flower': 'Vous mélangez paix et émotion. Vous êtes empathique mais discret, intuitif mais paisible.'
};

// Questions du test psychotechnique
export const PERSONALITY_QUESTIONS: Question[] = [
  // Questions Flower (1-4)
  { id: 1, question: "Vous vous laissez guider par vos émotions ?", expectedAnswer: true, classes: ['Flower'] },
  { id: 2, question: "Vous aimez aider les autres spontanément ?", expectedAnswer: true, classes: ['Flower'] },
  { id: 3, question: "Vous êtes souvent dans l'imaginaire ?", expectedAnswer: true, classes: ['Flower'] },
  { id: 4, question: "Vous appréciez les environnements créatifs ?", expectedAnswer: true, classes: ['Flower'] },
  
  // Questions Jewel (5-8)
  { id: 5, question: "Vous êtes très structuré dans votre quotidien ?", expectedAnswer: true, classes: ['Jewel'] },
  { id: 6, question: "Vous analysez beaucoup avant d'agir ?", expectedAnswer: true, classes: ['Jewel'] },
  { id: 7, question: "Vous aimez les listes, les tableaux, les plans ?", expectedAnswer: true, classes: ['Jewel'] },
  { id: 8, question: "Vous êtes plus logique que spontané ?", expectedAnswer: true, classes: ['Jewel'] },
  
  // Questions Shaker (9-12)
  { id: 9, question: "Vous aimez l'aventure et la nouveauté ?", expectedAnswer: true, classes: ['Shaker'] },
  { id: 10, question: "Vous agissez souvent sans réfléchir longtemps ?", expectedAnswer: true, classes: ['Shaker'] },
  { id: 11, question: "Vous aimez être dans l'action rapide ?", expectedAnswer: true, classes: ['Shaker'] },
  { id: 12, question: "Vous vous lassez vite de la routine ?", expectedAnswer: true, classes: ['Shaker'] },
  
  // Questions Stream (13-16)
  { id: 13, question: "Vous préférez la paix aux conflits ?", expectedAnswer: true, classes: ['Stream'] },
  { id: 14, question: "Vous prenez le temps avant de donner une réponse ?", expectedAnswer: true, classes: ['Stream'] },
  { id: 15, question: "Vous avez besoin d'un environnement calme ?", expectedAnswer: true, classes: ['Stream'] },
  { id: 16, question: "Vous évitez les confrontations ?", expectedAnswer: true, classes: ['Stream'] },
  
  // Questions intermédiaires Flower-Jewel (17-19)
  { id: 17, question: "Vous êtes sensible et organisé ?", expectedAnswer: true, classes: ['Flower-Jewel'] },
  { id: 18, question: "Vous êtes créatif mais aimez suivre une méthode ?", expectedAnswer: true, classes: ['Flower-Jewel'] },
  { id: 19, question: "Vous ressentez le besoin d'exprimer vos émotions dans un cadre structuré ?", expectedAnswer: true, classes: ['Flower-Jewel'] },
  
  // Questions intermédiaires Jewel-Shaker (20-22)
  { id: 20, question: "Vous aimez résoudre des problèmes en étant rapide ?", expectedAnswer: true, classes: ['Jewel-Shaker'] },
  { id: 21, question: "Vous êtes à la fois méthodique et énergique ?", expectedAnswer: true, classes: ['Jewel-Shaker'] },
  { id: 22, question: "Vous agissez vite mais de façon calculée ?", expectedAnswer: true, classes: ['Jewel-Shaker'] },
  
  // Questions intermédiaires Shaker-Stream (23-25)
  { id: 23, question: "Vous aimez improviser tout en restant adaptable ?", expectedAnswer: true, classes: ['Shaker-Stream'] },
  { id: 24, question: "Vous êtes dynamique sans être trop impulsif ?", expectedAnswer: true, classes: ['Shaker-Stream'] },
  { id: 25, question: "Vous aimez passer à l'action quand l'ambiance est calme ?", expectedAnswer: true, classes: ['Shaker-Stream'] },
  
  // Questions intermédiaires Stream-Flower (26-28)
  { id: 26, question: "Vous êtes émotif(ve) mais discret(ète) ?", expectedAnswer: true, classes: ['Stream-Flower'] },
  { id: 27, question: "Vous ressentez beaucoup sans forcément l'exprimer ?", expectedAnswer: true, classes: ['Stream-Flower'] },
  { id: 28, question: "Vous êtes paisible mais intuitif(ve) ?", expectedAnswer: true, classes: ['Stream-Flower'] },
  
  // Questions mixtes (29-32)
  { id: 29, question: "Vous vous retrouvez autant dans la logique que dans la sensibilité ?", expectedAnswer: true, classes: ['Flower-Jewel'] },
  { id: 30, question: "Vous êtes réactif(ve) mais posé(e) ?", expectedAnswer: true, classes: ['Shaker-Stream'] },
  { id: 31, question: "Vous êtes réfléchi(é) tout en appréciant la nouveauté ?", expectedAnswer: true, classes: ['Jewel-Shaker'] },
  { id: 32, question: "Vous êtes calme, empathique, et adaptable ?", expectedAnswer: true, classes: ['Stream-Flower'] }
];
