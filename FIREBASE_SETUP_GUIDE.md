# 🔥 Guide de Configuration Firebase pour ProfilingIris

## 📋 Étapes pour Connecter votre Projet Firebase

### 1. 🔑 Obtenir vos Clés Firebase

1. **Accédez à votre console Firebase** :
   ```
   https://console.firebase.google.com/project/profilingiris/settings/general
   ```

2. **<PERSON><PERSON> l'onglet "Général"** :
   - Faites défiler jusqu'à la section "Vos applications"
   - Si vous n'avez pas encore d'application web, cliquez sur "Ajouter une application" et sélectionnez l'icône web `</>`
   - Si vous avez déjà une application, cliquez sur l'icône de configuration (engrenage)

3. **Copiez la configuration** :
   Vous verrez quelque chose comme :
   ```javascript
   const firebaseConfig = {
     apiKey: "AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz",
     authDomain: "profilingiris.firebaseapp.com",
     projectId: "profilingiris",
     storageBucket: "profilingiris.appspot.com",
     messagingSenderId: "123456789012",
     appId: "1:123456789012:web:abcdef1234567890"
   };
   ```

### 2. 📝 Configurer votre Application

1. **Ouvrez le fichier** `src/environments/firebase.config.ts`

2. **Remplacez les valeurs** par vos vraies clés :
   ```typescript
   export const firebaseConfig = {
     apiKey: "VOTRE_VRAIE_API_KEY",
     authDomain: "profilingiris.firebaseapp.com",
     projectId: "profilingiris",
     storageBucket: "profilingiris.appspot.com",
     messagingSenderId: "VOTRE_VRAIE_SENDER_ID",
     appId: "VOTRE_VRAIE_APP_ID"
   };
   ```

### 3. 🔐 Configurer les Règles de Sécurité Firestore

1. **Accédez aux règles Firestore** :
   ```
   https://console.firebase.google.com/project/profilingiris/firestore/rules
   ```

2. **Remplacez les règles par** :
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // Permettre la lecture et l'écriture pour les utilisateurs authentifiés
       match /{document=**} {
         allow read, write: if true; // Temporaire pour les tests
       }
       
       // Règles plus strictes pour la production :
       /*
       match /personality_families/{familyId} {
         allow read: if true;
         allow write: if request.auth != null;
       }
       
       match /questions/{questionId} {
         allow read: if true;
         allow write: if request.auth != null;
       }
       
       match /user_responses/{responseId} {
         allow read, write: if request.auth != null;
       }
       
       match /personality_tests/{testId} {
         allow read, write: if request.auth != null;
       }
       
       match /session_stats/{sessionId} {
         allow read, write: if request.auth != null;
       }
       */
     }
   }
   ```

### 4. 🚀 Tester la Connexion

1. **Démarrez l'application** :
   ```bash
   ng serve
   ```

2. **Accédez à l'admin** :
   ```
   http://localhost:4200/admin
   ```

3. **Cliquez sur "Initialiser les données"** pour créer les collections de base

4. **Vérifiez dans Firebase Console** :
   ```
   https://console.firebase.google.com/project/profilingiris/firestore/data
   ```
   Vous devriez voir les collections :
   - `personality_families`
   - `questions`
   - `personality_descriptions`

### 5. 📊 Structure des Collections Firestore

Votre base de données aura cette structure :

```
profilingiris (Firestore Database)
├── personality_families/
│   ├── flower/
│   ├── jewel/
│   ├── shaker/
│   ├── stream/
│   ├── flower-jewel/
│   ├── jewel-shaker/
│   ├── shaker-stream/
│   └── stream-flower/
├── questions/
│   ├── 1/
│   ├── 2/
│   └── ... (32 questions)
├── personality_descriptions/
│   ├── Flower/
│   ├── Jewel/
│   └── ... (descriptions)
├── user_responses/
│   └── (réponses utilisateur)
├── personality_tests/
│   └── (sessions complètes)
└── session_stats/
    └── (statistiques)
```

### 6. 🔧 Dépannage

#### Erreur "Permission denied"
- Vérifiez les règles de sécurité Firestore
- Assurez-vous que `allow read, write: if true;` est configuré pour les tests

#### Erreur "Project not found"
- Vérifiez que `projectId: "profilingiris"` est correct
- Assurez-vous d'avoir accès au projet Firebase

#### Erreur "Invalid API key"
- Vérifiez que votre `apiKey` est correcte
- Régénérez la clé si nécessaire dans la console Firebase

#### Collections vides
- Cliquez sur "Initialiser les données" dans `/admin`
- Vérifiez la console du navigateur pour les erreurs

### 7. 📈 Utilisation

1. **Test de personnalité** : `/personality-test`
2. **Administration** : `/admin`
3. **Données en temps réel** : Toutes les réponses sont sauvegardées automatiquement

### 8. 🔒 Sécurité pour la Production

Avant de déployer en production, modifiez les règles Firestore :

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Lecture publique pour les données de base
    match /personality_families/{familyId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    match /questions/{questionId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Données utilisateur protégées
    match /user_responses/{responseId} {
      allow read, write: if request.auth != null;
    }
    
    match /personality_tests/{testId} {
      allow read, write: if request.auth != null;
    }
    
    match /session_stats/{sessionId} {
      allow read: if request.auth != null && request.auth.token.admin == true;
      allow write: if request.auth != null;
    }
  }
}
```

## ✅ Checklist de Vérification

- [ ] Clés Firebase configurées dans `firebase.config.ts`
- [ ] Règles Firestore configurées
- [ ] Application démarre sans erreur
- [ ] Page `/admin` accessible
- [ ] Données initialisées avec succès
- [ ] Test de personnalité fonctionnel
- [ ] Données visibles dans Firebase Console

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez la console du navigateur (F12)
2. Vérifiez les logs Firebase dans la console
3. Assurez-vous que toutes les dépendances sont installées : `npm install`
