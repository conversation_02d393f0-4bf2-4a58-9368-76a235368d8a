.page-container.suivantacc {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 20px 0;
  font-family: 'Montserrat', sans-serif;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 40px;

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 2rem;
      font-weight: 700;
      color: #333;

      span {
        font-weight: 400;
        font-style: italic;
      }
    }

    .nav {
      a {
        color: #555;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;

        &:after {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 0;
          height: 2px;
          background-color: var(--fleur-primary);
          transition: width 0.3s ease;
        }

        &:hover {
          color: var(--fleur-primary);

          &:after {
            width: 100%;
          }
        }
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60px;

    .main-card {
      width: 100%;
      max-width: 1000px;
      margin-bottom: 40px;
      perspective: 1000px;

      .card-inner {
        position: relative;
        width: 100%;
        transform-style: preserve-3d;
        transition: transform 0.6s;

        .card-front {
          width: 100%;
          backface-visibility: hidden;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
          background: white;
          display: flex;
          align-items: center;
          padding: 40px;
          gap: 40px;

          .image-container {
            flex: 1;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;

            .main-image {
              width: 300px;
              height: 300px;
              object-fit: cover;
              border-radius: 50%;
              box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
              z-index: 2;
              position: relative;
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.05);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
              }
            }

            .image-decoration {
              position: absolute;
              width: 330px;
              height: 330px;
              border-radius: 50%;
              border: 2px dashed var(--bijou-secondary);
              z-index: 1;
              animation: rotate 20s linear infinite;
            }
          }

          .text-content {
            flex: 1;

            .title {
              font-family: 'Playfair Display', serif;
              font-size: 2.2rem;
              color: #333;
              margin-bottom: 20px;
              position: relative;
            }

            .divider {
              width: 80px;
              height: 3px;
              background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
              margin-bottom: 20px;
              border-radius: 3px;
            }

            .intro {
              font-size: 1.2rem;
              font-weight: 500;
              color: #555;
              margin-bottom: 15px;
              font-style: italic;
            }

            .description {
              font-size: 1rem;
              line-height: 1.7;
              color: #666;
            }
          }
        }
      }
    }

    .action-container {
      display: flex;
      justify-content: center;

      .next-btn {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px 35px;
        background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
        color: white;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);
        }

        .icon {
          font-size: 1.2rem;
        }
      }
    }
  }

  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 60px;

    .feature-card {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      }

      .icon {
        font-size: 2.5rem;
        margin-bottom: 20px;
      }

      h3 {
        font-size: 1.3rem;
        color: #333;
        margin-bottom: 15px;
      }

      p {
        font-size: 0.95rem;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

// Animation pour la décoration de l'image
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Media queries pour la responsivité
@media (max-width: 992px) {
  .page-container.suivantacc {
    .content {
      .main-card {
        .card-inner {
          .card-front {
            flex-direction: column-reverse;
            padding: 30px;

            .text-content {
              margin-bottom: 30px;
              text-align: center;

              .title {
                font-size: 2rem;
              }

              .divider {
                margin: 0 auto 20px;
              }
            }

            .image-container {
              margin-bottom: 30px;

              .main-image {
                width: 250px;
                height: 250px;
              }

              .image-decoration {
                width: 280px;
                height: 280px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .page-container.suivantacc {
    .header {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .content {
      .main-card {
        .card-inner {
          .card-front {
            .text-content {
              .title {
                font-size: 1.8rem;
              }

              .intro {
                font-size: 1.1rem;
              }

              .description {
                font-size: 0.95rem;
              }
            }

            .image-container {
              .main-image {
                width: 200px;
                height: 200px;
              }

              .image-decoration {
                width: 230px;
                height: 230px;
              }
            }
          }
        }
      }
    }
  }
}
