<div class="auth-container signup">
  <div class="container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="title">Inscription</h1>
        <p class="subtitle">Créez votre compte IrisLock</p>
        <div class="divider"></div>
      </div>

      <div class="auth-form">
        <form (ngSubmit)="onSubmit()" #signupForm="ngForm">
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">Prénom</label>
              <div class="input-container">
                <span class="input-icon">👤</span>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  [(ngModel)]="signupData.firstName"
                  required
                  #firstName="ngModel"
                  placeholder="Votre prénom"
                >
              </div>
              <div class="error-message" *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)">
                <span *ngIf="firstName.errors?.['required']">Le prénom est requis</span>
              </div>
            </div>

            <div class="form-group">
              <label for="lastName">Nom</label>
              <div class="input-container">
                <span class="input-icon">👤</span>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  [(ngModel)]="signupData.lastName"
                  required
                  #lastName="ngModel"
                  placeholder="Votre nom"
                >
              </div>
              <div class="error-message" *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)">
                <span *ngIf="lastName.errors?.['required']">Le nom est requis</span>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <div class="input-container">
              <span class="input-icon">✉️</span>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="signupData.email"
                required
                email
                #email="ngModel"
                placeholder="Votre adresse email"
              >
            </div>
            <div class="error-message" *ngIf="email.invalid && (email.dirty || email.touched)">
              <span *ngIf="email.errors?.['required']">L'email est requis</span>
              <span *ngIf="email.errors?.['email']">Veuillez entrer un email valide</span>
            </div>
          </div>

          <div class="form-group">
            <label for="password">Mot de passe</label>
            <div class="input-container">
              <span class="input-icon">🔒</span>
              <input
                [type]="showPassword ? 'text' : 'password'"
                id="password"
                name="password"
                [(ngModel)]="signupData.password"
                required
                minlength="6"
                #password="ngModel"
                placeholder="Créez un mot de passe"
              >
              <button
                type="button"
                class="toggle-password"
                (click)="togglePasswordVisibility()"
              >
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
            <div class="error-message" *ngIf="password.invalid && (password.dirty || password.touched)">
              <span *ngIf="password.errors?.['required']">Le mot de passe est requis</span>
              <span *ngIf="password.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caractères</span>
            </div>
          </div>

          <div class="form-group">
            <label for="confirmPassword">Confirmer le mot de passe</label>
            <div class="input-container">
              <span class="input-icon">🔒</span>
              <input
                [type]="showPassword ? 'text' : 'password'"
                id="confirmPassword"
                name="confirmPassword"
                [(ngModel)]="signupData.confirmPassword"
                required
                #confirmPassword="ngModel"
                placeholder="Confirmez votre mot de passe"
              >
            </div>
            <div class="error-message" *ngIf="confirmPassword.dirty && signupData.password !== signupData.confirmPassword">
              <span>Les mots de passe ne correspondent pas</span>
            </div>
          </div>

          <div class="form-group terms">
            <div class="checkbox-container">
              <input
                type="checkbox"
                id="terms"
                name="terms"
                [(ngModel)]="signupData.termsAccepted"
                required
                #terms="ngModel"
              >
              <label for="terms">J'accepte les <a href="#">conditions d'utilisation</a> et la <a href="#">politique de confidentialité</a></label>
            </div>
            <div class="error-message" *ngIf="terms.invalid && (terms.dirty || terms.touched)">
              <span *ngIf="terms.errors?.['required']">Vous devez accepter les conditions d'utilisation</span>
            </div>
          </div>

          <div class="form-actions">
            <button
              type="submit"
              class="submit-btn"
              [disabled]="signupForm.invalid || isLoading || signupData.password !== signupData.confirmPassword"
            >
              <span *ngIf="!isLoading">Créer un compte</span>
              <span *ngIf="isLoading">Création en cours...</span>
            </button>
          </div>
        </form>

        <div class="social-login">
          <p class="separator">Ou inscrivez-vous avec</p>
          <div class="social-buttons">
            <button class="social-btn google">
              <img src="assets/google-icon.png" alt="Google">
              Google
            </button>
            <button class="social-btn facebook">
              <span class="facebook-icon">f</span>
              Facebook
            </button>
          </div>
        </div>

        <div class="auth-footer">
          <p>Vous avez déjà un compte? <a routerLink="/login">Connectez-vous</a></p>
        </div>
      </div>
    </div>

    <div class="navigation">
      <a routerLink="/accueil" class="back-btn">
        <span class="icon">←</span>
        <span>Retour à l'accueil</span>
      </a>
    </div>
  </div>
</div>
