import { Injectable } from '@angular/core';
import { Firestore, collection, addDoc, doc, updateDoc, getDocs, query, where, orderBy } from '@angular/fire/firestore';
import { Observable, from } from 'rxjs';
import { MockFirebaseService } from './mock-firebase.service';
import {
  TestSession,
  UserResponse,
  PersonalityScores,
  PersonalityProfile,
  PersonalityClass,
  PERSONALITY_QUESTIONS,
  PERSONALITY_DESCRIPTIONS
} from '../models/personality-test.model';

@Injectable({
  providedIn: 'root'
})
export class PersonalityTestService {
  private readonly COLLECTION_NAME = 'personality_tests';
  private readonly USE_MOCK = true; // Mettre à false pour utiliser Firebase réel

  constructor(
    private firestore: Firestore,
    private mockFirebaseService: MockFirebaseService
  ) {}

  /**
   * Calcule les scores pour chaque classe de personnalité
   */
  calculateScores(responses: UserResponse[]): PersonalityScores {
    const scores: PersonalityScores = {
      flower: 0,
      jewel: 0,
      shaker: 0,
      stream: 0,
      flowerJewel: 0,
      jewelShaker: 0,
      shakerStream: 0,
      streamFlower: 0
    };

    responses.forEach(response => {
      const question = PERSONALITY_QUESTIONS.find(q => q.id === response.questionId);
      if (!question) return;

      const isCorrectAnswer = response.answer === question.expectedAnswer;
      if (!isCorrectAnswer) return;

      // Attribution des points selon les classes de la question
      question.classes.forEach(className => {
        switch (className) {
          case 'Flower':
            scores.flower += 1;
            break;
          case 'Jewel':
            scores.jewel += 1;
            break;
          case 'Shaker':
            scores.shaker += 1;
            break;
          case 'Stream':
            scores.stream += 1;
            break;
          case 'Flower-Jewel':
            scores.flowerJewel += 1;
            // Contribue aussi aux classes de base
            scores.flower += 0.5;
            scores.jewel += 0.5;
            break;
          case 'Jewel-Shaker':
            scores.jewelShaker += 1;
            scores.jewel += 0.5;
            scores.shaker += 0.5;
            break;
          case 'Shaker-Stream':
            scores.shakerStream += 1;
            scores.shaker += 0.5;
            scores.stream += 0.5;
            break;
          case 'Stream-Flower':
            scores.streamFlower += 1;
            scores.stream += 0.5;
            scores.flower += 0.5;
            break;
        }
      });
    });

    return scores;
  }

  /**
   * Détermine le profil de personnalité basé sur les scores
   */
  determineProfile(scores: PersonalityScores): PersonalityProfile {
    // Scores des classes de base
    const baseScores = [
      { class: 'Flower' as PersonalityClass, score: scores.flower },
      { class: 'Jewel' as PersonalityClass, score: scores.jewel },
      { class: 'Shaker' as PersonalityClass, score: scores.shaker },
      { class: 'Stream' as PersonalityClass, score: scores.stream }
    ];

    // Scores des classes intermédiaires
    const intermediateScores = [
      { class: 'Flower-Jewel' as PersonalityClass, score: scores.flowerJewel },
      { class: 'Jewel-Shaker' as PersonalityClass, score: scores.jewelShaker },
      { class: 'Shaker-Stream' as PersonalityClass, score: scores.shakerStream },
      { class: 'Stream-Flower' as PersonalityClass, score: scores.streamFlower }
    ];

    // Trier par score décroissant
    baseScores.sort((a, b) => b.score - a.score);
    intermediateScores.sort((a, b) => b.score - a.score);

    const maxBase = baseScores[0];
    const secondMaxBase = baseScores[1];
    const maxIntermediate = intermediateScores[0];

    // Logique de détermination du profil
    let primaryClass: PersonalityClass;
    let secondaryClass: PersonalityClass | undefined;
    let isIntermediate = false;
    let confidenceScore = 0;

    // Si une classe intermédiaire a un score élevé
    if (maxIntermediate.score >= 2) {
      primaryClass = maxIntermediate.class;
      isIntermediate = true;
      confidenceScore = (maxIntermediate.score / 3) * 100; // Max 3 questions par classe intermédiaire
    }
    // Si deux classes de base sont proches et élevées
    else if (maxBase.score >= 2 && secondMaxBase.score >= 2 && (maxBase.score - secondMaxBase.score) <= 1) {
      // Créer un profil intermédiaire
      const combinedClasses = [maxBase.class, secondMaxBase.class].sort();
      primaryClass = `${combinedClasses[0]}-${combinedClasses[1]}` as PersonalityClass;
      isIntermediate = true;
      confidenceScore = ((maxBase.score + secondMaxBase.score) / 8) * 100; // Max 4 questions par classe de base
    }
    // Sinon, classe dominante
    else {
      primaryClass = maxBase.class;
      if (secondMaxBase.score >= 1) {
        secondaryClass = secondMaxBase.class;
      }
      confidenceScore = (maxBase.score / 4) * 100; // Max 4 questions par classe de base
    }

    return {
      primaryClass,
      secondaryClass,
      isIntermediate,
      confidenceScore: Math.round(confidenceScore),
      description: PERSONALITY_DESCRIPTIONS[primaryClass]
    };
  }

  /**
   * Sauvegarde une session de test dans Firebase
   */
  saveTestSession(session: TestSession): Observable<string> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.saveTestSession(session);
    }

    const testCollection = collection(this.firestore, this.COLLECTION_NAME);
    return from(addDoc(testCollection, {
      ...session,
      completedAt: session.completedAt,
      startedAt: session.startedAt
    }).then(docRef => docRef.id));
  }

  /**
   * Met à jour une session de test existante
   */
  updateTestSession(sessionId: string, updates: Partial<TestSession>): Observable<void> {
    const sessionDoc = doc(this.firestore, this.COLLECTION_NAME, sessionId);
    return from(updateDoc(sessionDoc, updates));
  }

  /**
   * Récupère toutes les sessions de test
   */
  getAllTestSessions(): Observable<TestSession[]> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.getAllTestSessions();
    }

    const testCollection = collection(this.firestore, this.COLLECTION_NAME);
    const q = query(testCollection, orderBy('completedAt', 'desc'));

    return from(getDocs(q).then(snapshot =>
      snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as TestSession))
    ));
  }

  /**
   * Récupère les sessions de test d'un utilisateur spécifique
   */
  getUserTestSessions(userEmail: string): Observable<TestSession[]> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.getUserTestSessions(userEmail);
    }

    const testCollection = collection(this.firestore, this.COLLECTION_NAME);
    const q = query(
      testCollection,
      where('userEmail', '==', userEmail),
      orderBy('completedAt', 'desc')
    );

    return from(getDocs(q).then(snapshot =>
      snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as TestSession))
    ));
  }

  /**
   * Crée une nouvelle session de test
   */
  createTestSession(userName?: string, userEmail?: string): TestSession {
    return {
      userName: userName || 'Utilisateur anonyme',
      userEmail: userEmail || '',
      responses: [],
      scores: {
        flower: 0,
        jewel: 0,
        shaker: 0,
        stream: 0,
        flowerJewel: 0,
        jewelShaker: 0,
        shakerStream: 0,
        streamFlower: 0
      },
      finalProfile: {
        primaryClass: 'Flower',
        isIntermediate: false,
        confidenceScore: 0,
        description: ''
      },
      startedAt: new Date(),
      completedAt: new Date()
    };
  }

  /**
   * Traite les réponses et calcule le profil final
   */
  processTestResults(responses: UserResponse[]): { scores: PersonalityScores, profile: PersonalityProfile } {
    const scores = this.calculateScores(responses);
    const profile = this.determineProfile(scores);

    return { scores, profile };
  }
}
