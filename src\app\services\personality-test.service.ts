import { Injectable } from '@angular/core';
import { Database, ref, push, set, get, query, orderByChild, equalTo } from '@angular/fire/database';
import { Observable, from } from 'rxjs';
import { MockFirebaseService } from './mock-firebase.service';
import { FirebaseDataInitService } from './firebase-data-init.service';
import {
  TestSession,
  UserResponse,
  PersonalityScores,
  PersonalityProfile,
  PersonalityClass,
  PERSONALITY_QUESTIONS,
  PERSONALITY_DESCRIPTIONS
} from '../models/personality-test.model';

@Injectable({
  providedIn: 'root'
})
export class PersonalityTestService {
  private readonly USE_MOCK = false; // Utiliser Firebase réel maintenant

  constructor(
    private database: Database,
    private mockFirebaseService: MockFirebaseService,
    private firebaseDataInitService: FirebaseDataInitService
  ) {
    // Initialiser les données de base au démarrage
    this.initializeFirebaseData();
  }

  /**
   * Initialise les données de base dans Firebase
   */
  private initializeFirebaseData(): void {
    if (!this.USE_MOCK) {
      this.firebaseDataInitService.initializeBaseData().subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Données Firebase initialisées');
          } else {
            console.error('❌ Erreur lors de l\'initialisation Firebase');
          }
        },
        error: (error) => {
          console.error('❌ Erreur Firebase:', error);
        }
      });
    }
  }

  /**
   * Calcule les scores pour chaque classe de personnalité
   */
  calculateScores(responses: UserResponse[]): PersonalityScores {
    const scores: PersonalityScores = {
      flower: 0,
      jewel: 0,
      shaker: 0,
      stream: 0,
      flowerJewel: 0,
      jewelShaker: 0,
      shakerStream: 0,
      streamFlower: 0
    };

    responses.forEach(response => {
      const question = PERSONALITY_QUESTIONS.find(q => q.id === response.questionId);
      if (!question) return;

      const isCorrectAnswer = response.answer === question.expectedAnswer;
      if (!isCorrectAnswer) return;

      // Attribution des points selon les classes de la question
      question.classes.forEach(className => {
        switch (className) {
          case 'Flower':
            scores.flower += 1;
            break;
          case 'Jewel':
            scores.jewel += 1;
            break;
          case 'Shaker':
            scores.shaker += 1;
            break;
          case 'Stream':
            scores.stream += 1;
            break;
          case 'Flower-Jewel':
            scores.flowerJewel += 1;
            // Contribue aussi aux classes de base
            scores.flower += 0.5;
            scores.jewel += 0.5;
            break;
          case 'Jewel-Shaker':
            scores.jewelShaker += 1;
            scores.jewel += 0.5;
            scores.shaker += 0.5;
            break;
          case 'Shaker-Stream':
            scores.shakerStream += 1;
            scores.shaker += 0.5;
            scores.stream += 0.5;
            break;
          case 'Stream-Flower':
            scores.streamFlower += 1;
            scores.stream += 0.5;
            scores.flower += 0.5;
            break;
        }
      });
    });

    return scores;
  }

  /**
   * Détermine le profil de personnalité basé sur les scores
   */
  determineProfile(scores: PersonalityScores): PersonalityProfile {
    // Scores des classes de base
    const baseScores = [
      { class: 'Flower' as PersonalityClass, score: scores.flower },
      { class: 'Jewel' as PersonalityClass, score: scores.jewel },
      { class: 'Shaker' as PersonalityClass, score: scores.shaker },
      { class: 'Stream' as PersonalityClass, score: scores.stream }
    ];

    // Scores des classes intermédiaires
    const intermediateScores = [
      { class: 'Flower-Jewel' as PersonalityClass, score: scores.flowerJewel },
      { class: 'Jewel-Shaker' as PersonalityClass, score: scores.jewelShaker },
      { class: 'Shaker-Stream' as PersonalityClass, score: scores.shakerStream },
      { class: 'Stream-Flower' as PersonalityClass, score: scores.streamFlower }
    ];

    // Trier par score décroissant
    baseScores.sort((a, b) => b.score - a.score);
    intermediateScores.sort((a, b) => b.score - a.score);

    const maxBase = baseScores[0];
    const secondMaxBase = baseScores[1];
    const maxIntermediate = intermediateScores[0];

    // Logique de détermination du profil
    let primaryClass: PersonalityClass;
    let secondaryClass: PersonalityClass | undefined;
    let isIntermediate = false;
    let confidenceScore = 0;

    // Si une classe intermédiaire a un score élevé
    if (maxIntermediate.score >= 2) {
      primaryClass = maxIntermediate.class;
      isIntermediate = true;
      confidenceScore = (maxIntermediate.score / 3) * 100; // Max 3 questions par classe intermédiaire
    }
    // Si deux classes de base sont proches et élevées
    else if (maxBase.score >= 2 && secondMaxBase.score >= 2 && (maxBase.score - secondMaxBase.score) <= 1) {
      // Créer un profil intermédiaire
      const combinedClasses = [maxBase.class, secondMaxBase.class].sort();
      primaryClass = `${combinedClasses[0]}-${combinedClasses[1]}` as PersonalityClass;
      isIntermediate = true;
      confidenceScore = ((maxBase.score + secondMaxBase.score) / 8) * 100; // Max 4 questions par classe de base
    }
    // Sinon, classe dominante
    else {
      primaryClass = maxBase.class;
      if (secondMaxBase.score >= 1) {
        secondaryClass = secondMaxBase.class;
      }
      confidenceScore = (maxBase.score / 4) * 100; // Max 4 questions par classe de base
    }

    return {
      primaryClass,
      secondaryClass,
      isIntermediate,
      confidenceScore: Math.round(confidenceScore),
      description: PERSONALITY_DESCRIPTIONS[primaryClass]
    };
  }

  /**
   * Sauvegarde une session de test dans Firebase Realtime Database
   */
  saveTestSession(session: TestSession): Observable<string> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.saveTestSession(session);
    }

    const testSessionsRef = ref(this.database, 'personality_tests');
    const newSessionRef = push(testSessionsRef);

    const sessionData = {
      ...session,
      completedAt: session.completedAt?.toISOString(),
      startedAt: session.startedAt?.toISOString(),
      responses: session.responses.map(response => ({
        ...response,
        timestamp: response.timestamp.toISOString()
      }))
    };

    return from(set(newSessionRef, sessionData).then(() => newSessionRef.key!));
  }

  /**
   * Sauvegarde une réponse individuelle en temps réel
   */
  saveIndividualResponse(userId: string, sessionId: string, response: UserResponse): Observable<boolean> {
    if (this.USE_MOCK) {
      return from(Promise.resolve(true));
    }

    const responseData = {
      ...response,
      timestamp: response.timestamp.toISOString(),
      userId,
      sessionId
    };

    const responseRef = ref(this.database, `user_responses/${userId}/${sessionId}/${response.questionId}`);
    return from(set(responseRef, responseData).then(() => true).catch(() => false));
  }

  /**
   * Sauvegarde les statistiques de session
   */
  saveSessionStats(sessionId: string, stats: any): Observable<boolean> {
    if (this.USE_MOCK) {
      return from(Promise.resolve(true));
    }

    const statsRef = ref(this.database, `session_stats/${sessionId}`);
    return from(set(statsRef, {
      ...stats,
      timestamp: new Date().toISOString()
    }).then(() => true).catch(() => false));
  }

  /**
   * Met à jour une session de test existante
   */
  updateTestSession(sessionId: string, updates: Partial<TestSession>): Observable<void> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.updateTestSession(sessionId, updates);
    }

    const sessionRef = ref(this.database, `personality_tests/${sessionId}`);
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };

    return from(set(sessionRef, updateData).then(() => void 0));
  }

  /**
   * Récupère toutes les sessions de test
   */
  getAllTestSessions(): Observable<TestSession[]> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.getAllTestSessions();
    }

    const testsRef = ref(this.database, 'personality_tests');
    return from(get(testsRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        return Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        } as TestSession)).sort((a, b) =>
          new Date(b.completedAt || 0).getTime() - new Date(a.completedAt || 0).getTime()
        );
      }
      return [];
    }));
  }

  /**
   * Récupère les sessions de test d'un utilisateur spécifique
   */
  getUserTestSessions(userEmail: string): Observable<TestSession[]> {
    if (this.USE_MOCK) {
      return this.mockFirebaseService.getUserTestSessions(userEmail);
    }

    const testsRef = ref(this.database, 'personality_tests');
    return from(get(testsRef).then(snapshot => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        return Object.keys(data)
          .map(key => ({
            id: key,
            ...data[key]
          } as TestSession))
          .filter(session => session.userEmail === userEmail)
          .sort((a, b) =>
            new Date(b.completedAt || 0).getTime() - new Date(a.completedAt || 0).getTime()
          );
      }
      return [];
    }));
  }

  /**
   * Crée une nouvelle session de test
   */
  createTestSession(userName?: string, userEmail?: string): TestSession {
    return {
      userName: userName || 'Utilisateur anonyme',
      userEmail: userEmail || '',
      responses: [],
      scores: {
        flower: 0,
        jewel: 0,
        shaker: 0,
        stream: 0,
        flowerJewel: 0,
        jewelShaker: 0,
        shakerStream: 0,
        streamFlower: 0
      },
      finalProfile: {
        primaryClass: 'Flower',
        isIntermediate: false,
        confidenceScore: 0,
        description: ''
      },
      startedAt: new Date(),
      completedAt: new Date()
    };
  }

  /**
   * Traite les réponses et calcule le profil final
   */
  processTestResults(responses: UserResponse[]): { scores: PersonalityScores, profile: PersonalityProfile } {
    const scores = this.calculateScores(responses);
    const profile = this.determineProfile(scores);

    return { scores, profile };
  }
}
