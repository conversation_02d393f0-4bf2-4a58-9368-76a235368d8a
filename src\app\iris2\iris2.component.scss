.iris-types-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 40px 0;

  .header {
    text-align: center;
    margin-bottom: 50px;

    .title {
      font-size: 2.8rem;
      color: #333;
      margin-bottom: 15px;
      position: relative;
      display: inline-block;

      &:after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary), var(--flux-primary), var(--shaker-primary));
        border-radius: 3px;
      }
    }

    .subtitle {
      color: #666;
      font-size: 1.2rem;
      font-weight: 300;
      max-width: 700px;
      margin: 0 auto;
      margin-top: 20px;
    }
  }

  .iris-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
  }

  .iris-card {
    height: 350px;
    perspective: 1000px;

    .card-inner {
      position: relative;
      width: 100%;
      height: 100%;
      transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      transform-style: preserve-3d;

      &:hover {
        transform: rotateY(180deg);
      }
    }

    .card-front, .card-back {
      position: absolute;
      width: 100%;
      height: 100%;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease;
    }

    &:hover .card-front, &:hover .card-back {
      box-shadow: 0 15px 40px rgba(106, 90, 205, 0.2);
    }

    .card-front {
      background: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;

      .image-container {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        &:hover img {
          transform: scale(1.1) rotate(5deg);
        }
      }

      .iris-name {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .iris-tagline {
        font-size: 1rem;
        color: #666;
        font-style: italic;
      }
    }

    .card-back {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px;
      transform: rotateY(180deg);
      text-align: center;

      h3 {
        font-size: 1.8rem;
        margin-bottom: 15px;
      }

      p {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 25px;
      }

      .discover-btn {
        padding: 10px 25px;
        border-radius: 50px;
        font-weight: 500;
        color: white;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
      }
    }

    // Styles spécifiques pour chaque type d'iris
    &.fleur, &.bijou, &.flux, &.shaker {
      .card-front {
        .iris-name {
          color: var(--fleur-primary);
        }
      }

      .card-back {
        background: linear-gradient(135deg, var(--fleur-primary), var(--bijou-primary));

        h3 {
          color: white;
        }

        .discover-btn {
          background-color: white;
          color: var(--fleur-primary);
          box-shadow: 0 5px 15px rgba(106, 90, 205, 0.3);

          &:hover {
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 8px 20px rgba(106, 90, 205, 0.4);
            transform: translateY(-3px);
          }
        }
      }
    }

    // Conserver les couleurs originales pour les noms sur le recto
    &.bijou .card-front .iris-name {
      color: var(--bijou-primary);
    }

    &.flux .card-front .iris-name {
      color: var(--flux-primary);
    }

    &.shaker .card-front .iris-name {
      color: var(--shaker-primary);
    }
  }

  .navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;

    .btn {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 25px;
      border-radius: 50px;
      font-weight: 500;
      transition: all 0.3s ease;
      text-decoration: none;

      &:hover {
        transform: translateY(-3px);
      }

      .icon {
        font-size: 1.2rem;
      }
    }

    .back-btn {
      background-color: var(--fleur-primary);
      color: white;
      box-shadow: 0 10px 25px rgba(106, 90, 205, 0.3);

      &:hover {
        background-color: darken(#6a5acd, 10%);
        box-shadow: 0 15px 35px rgba(106, 90, 205, 0.4);
      }
    }

    .diversity-btn {
      background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
      color: white;
      box-shadow: 0 10px 25px rgba(106, 90, 205, 0.3);

      &:hover {
        box-shadow: 0 15px 35px rgba(106, 90, 205, 0.4);
      }
    }
  }
}

// Media queries pour la responsivité
@media (max-width: 768px) {
  .iris-types-container {
    .header {
      .title {
        font-size: 2.2rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    .iris-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .iris-card {
      height: 300px;

      .card-front {
        .image-container {
          width: 120px;
          height: 120px;
        }

        .iris-name {
          font-size: 1.5rem;
        }
      }

      .card-back {
        padding: 20px;

        h3 {
          font-size: 1.5rem;
        }

        p {
          font-size: 0.9rem;
        }
      }
    }
  }
}
