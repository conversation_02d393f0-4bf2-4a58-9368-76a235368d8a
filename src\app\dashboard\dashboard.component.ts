import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  // Données utilisateur
  userName: string = '<PERSON>';
  lastScanTime: string = '10:30';
  securityLevel: string = 'Élevé';
  profileStatus: string = 'Vérifié';

  // Données de l'iris
  irisType: string = 'Crypte Dominant';
  irisColor: string = 'Marron';
  uniqueFeatures: number = 42;
  confidenceScore: string = '98.7%';

  // Activités récentes
  recentActivities = [
    { type: 'scan', title: 'Scan d\'iris complété', time: 'Aujourd\'hui à 10:30' },
    { type: 'profile', title: 'Profil mis à jour', time: 'Hier à 14:15' },
    { type: 'report', title: 'Rapport généré', time: 'Il y a 3 jours à 11:45' }
  ];

  // Statuts de sécurité
  securityStatuses = [
    { type: 'data', title: 'Protection des données', description: 'Vos données biométriques sont cryptées' },
    { type: 'biometric', title: 'Authentification biométrique', description: 'Activée pour une sécurité renforcée' },
    { type: 'compliance', title: 'Conformité RGPD', description: 'Conforme aux réglementations de protection des données' }
  ];

  // Onglet actif
  activeTab: string = 'overview';

  constructor(private router: Router) { }

  ngOnInit(): void {
    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)
    // Si non connecté, rediriger vers la page de connexion
    // this.checkAuthentication();
  }

  // Méthode pour naviguer vers la page de scan d'iris
  startNewScan(): void {
    alert('Lancement d\'un nouveau scan d\'iris...');
    // Redirection vers la page de scan (à implémenter)
    // this.router.navigate(['/scan-iris']);
  }

  // Méthode pour changer d'onglet dans le tableau de bord
  changeTab(tab: string): void {
    this.activeTab = tab;
    alert(`Navigation vers l'onglet: ${tab}`);
    // Implémenter la logique de changement d'onglet
  }

  // Méthodes pour les cartes d'information
  showSecurityDetails(): void {
    alert('Détails du niveau de sécurité');
  }

  showProfileStatus(): void {
    alert('Détails du statut du profil');
  }

  showScanHistory(): void {
    alert('Historique des scans');
  }

  // Méthodes pour l'aperçu du profil d'iris
  viewFullIrisImage(): void {
    alert('Affichage de l\'image d\'iris en plein écran');
  }

  showPatternDetails(): void {
    alert(`Détails du type de motif: ${this.irisType}`);
  }

  showColorDetails(): void {
    alert(`Détails de la couleur d'iris: ${this.irisColor}`);
  }

  showFeaturesDetails(): void {
    alert(`Détails des caractéristiques uniques: ${this.uniqueFeatures} points identifiés`);
  }

  showConfidenceDetails(): void {
    alert(`Détails du score de confiance: ${this.confidenceScore}`);
  }

  showVerificationDetails(): void {
    alert(`Détails de la vérification effectuée à ${this.lastScanTime}`);
  }

  // Méthodes pour l'activité récente
  viewScanDetails(): void {
    alert('Détails du scan d\'iris complété');
  }

  viewProfileUpdateDetails(): void {
    alert('Détails de la mise à jour du profil');
  }

  viewReportDetails(): void {
    alert('Détails du rapport généré');
  }

  // Méthodes pour le statut de sécurité
  viewDataProtectionDetails(): void {
    alert('Détails de la protection des données');
  }

  viewBiometricAuthDetails(): void {
    alert('Détails de l\'authentification biométrique');
  }

  viewGDPRComplianceDetails(): void {
    alert('Détails de la conformité RGPD');
  }

  // Méthode pour vérifier l'authentification (à implémenter)
  private checkAuthentication(): void {
    const isAuthenticated = false; // À remplacer par la vérification réelle

    if (!isAuthenticated) {
      this.router.navigate(['/login']);
    }
  }
}
