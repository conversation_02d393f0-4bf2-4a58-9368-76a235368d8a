// Configuration Firebase pour ProfilingIris
// IMPORTANT: Remplacez ces valeurs par vos vraies clés Firebase

export const firebaseConfig = {
  apiKey: "VOTRE_API_KEY_ICI", // Remplacez par votre vraie clé API
  authDomain: "profilingiris.firebaseapp.com",
  projectId: "profilingiris",
  storageBucket: "profilingiris.appspot.com",
  messagingSenderId: "VOTRE_SENDER_ID", // Remplacez par votre sender ID
  appId: "VOTRE_APP_ID" // Remplacez par votre app ID
};

// Pour obtenir vos vraies clés Firebase :
// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general
// 2. Cliquez sur "Ajouter une application" ou sélectionnez votre app web existante
// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus

// Exemple de configuration complète :
/*
export const firebaseConfig = {
  apiKey: "AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz",
  authDomain: "profilingiris.firebaseapp.com",
  projectId: "profilingiris",
  storageBucket: "profilingiris.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef1234567890"
};
*/
