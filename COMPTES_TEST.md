# Comptes de Test - Système PFA

## 📋 Comptes Statiques Disponibles

### 🔐 Comptes de Connexion

| Nom | Email | Mot de passe | Rôle | Description |
|-----|-------|--------------|------|-------------|
| **Administrateur Test** | `<EMAIL>` | `admin123` | Admin | Compte administrateur avec tous les privilèges |
| **Utilisateur Test** | `<EMAIL>` | `user123` | User | Compte utilisateur standard |
| **<PERSON>** | `<EMAIL>` | `marie123` | User | Profil créatif et émotionnel |
| **<PERSON>** | `<EMAIL>` | `jean123` | User | Profil analytique et structuré |

## 🧠 Test de Personnalité

### Utilisateurs Prédéfinis pour le Test Psychotechnique

| ID | Nom | Email | Description du Profil |
|----|-----|-------|----------------------|
| 1 | **<PERSON>** | `<EMAIL>` | Profil créatif et émotionnel (Flower) |
| 2 | **Jean Martin** | `<EMAIL>` | Profil analytique et structuré (Jewel) |
| 3 | **Sophie Leroy** | `<EMAIL>` | Profil dynamique et aventurier (Shaker) |
| 4 | **Pierre Moreau** | `<EMAIL>` | Profil paisible et réfléchi (Stream) |
| 5 | **Emma Bernard** | `<EMAIL>` | Profil mixte créatif-analytique (Flower-Jewel) |

## 🚀 Comment Utiliser les Comptes de Test

### 1. Connexion
1. Allez sur la page de connexion : `http://localhost:4200/login`
2. Cliquez sur l'une des cartes de compte de test
3. Le formulaire se remplit automatiquement
4. Cliquez sur "Se connecter"

### 2. Test de Personnalité
1. Allez sur : `http://localhost:4200/personality-test`
2. Le système utilise automatiquement un compte de test prédéfini
3. Répondez aux 32 questions
4. Consultez vos résultats

## 📊 Classes de Personnalité

### Classes Principales
- **Flower** 🌸 : Émotionnel, créatif, empathique
- **Jewel** 💎 : Structuré, analytique, méthodique  
- **Shaker** ⚡ : Dynamique, aventurier, spontané
- **Stream** 🌊 : Paisible, réfléchi, diplomate

### Classes Intermédiaires
- **Flower-Jewel** 🌸💎 : Sensibilité + Organisation
- **Jewel-Shaker** 💎⚡ : Méthode + Dynamisme
- **Shaker-Stream** ⚡🌊 : Action + Réflexion
- **Stream-Flower** 🌊🌸 : Paix + Émotion

## 🔧 Configuration Technique

### Base de Données
- **Nom** : PFA1 (Firebase)
- **Mode** : Mock (localStorage) pour les tests
- **Collections** : `personality_tests`

### Stockage Local
Les résultats des tests sont sauvegardés dans le localStorage du navigateur :
- `pfa_test_sessions` : Sessions de test
- `pfa_next_id` : Prochain ID disponible
- `currentUser` : Utilisateur connecté

## 🎯 Fonctionnalités Testables

### ✅ Connexion
- [x] Validation des identifiants
- [x] Remplissage automatique du formulaire
- [x] Redirection après connexion
- [x] Sauvegarde de la session utilisateur

### ✅ Test de Personnalité
- [x] 32 questions psychotechniques
- [x] Algorithme de scoring
- [x] Calcul des profils intermédiaires
- [x] Sauvegarde des résultats
- [x] Affichage des résultats détaillés

### ✅ Interface Utilisateur
- [x] Design responsive
- [x] Animations et transitions
- [x] Barre de progression
- [x] Écrans de chargement
- [x] Gestion des erreurs

## 🐛 Dépannage

### Problèmes Courants

1. **Erreur de connexion**
   - Vérifiez que vous utilisez les bons identifiants
   - Utilisez les comptes de test fournis

2. **Test ne se sauvegarde pas**
   - Vérifiez la console du navigateur
   - Le mode mock utilise localStorage

3. **Page blanche**
   - Vérifiez que le serveur de développement fonctionne
   - Ouvrez la console pour voir les erreurs

### Commandes Utiles

```bash
# Démarrer le serveur de développement
npm start

# Vérifier les erreurs de compilation
ng build

# Nettoyer le cache
npm run ng cache clean
```

## 📝 Notes de Développement

- Le système utilise actuellement un service mock pour simuler Firebase
- Pour activer Firebase réel, modifier `USE_MOCK = false` dans `PersonalityTestService`
- Les comptes de test sont codés en dur pour faciliter les démonstrations
- Tous les mots de passe sont en clair pour les tests (à ne pas faire en production)

---

**Version** : 1.0  
**Dernière mise à jour** : 27/05/2025  
**Développé pour** : Projet PFA - Test de Personnalité
