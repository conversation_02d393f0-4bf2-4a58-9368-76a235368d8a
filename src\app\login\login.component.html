<div class="auth-container login">
  <div class="container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="title">Connexion</h1>
        <p class="subtitle">Accédez à votre compte IrisLock</p>
        <div class="divider"></div>
      </div>

      <div class="auth-form">
        <form (ngSubmit)="onSubmit()" #loginForm="ngForm">
          <div class="form-group">
            <label for="email">Email</label>
            <div class="input-container">
              <span class="input-icon">✉️</span>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="loginData.email"
                required
                email
                #email="ngModel"
                placeholder="Votre adresse email"
              >
            </div>
            <div class="error-message" *ngIf="email.invalid && (email.dirty || email.touched)">
              <span *ngIf="email.errors?.['required']">L'email est requis</span>
              <span *ngIf="email.errors?.['email']">Veuillez entrer un email valide</span>
            </div>
          </div>

          <div class="form-group">
            <label for="password">Mot de passe</label>
            <div class="input-container">
              <span class="input-icon">🔒</span>
              <input
                [type]="showPassword ? 'text' : 'password'"
                id="password"
                name="password"
                [(ngModel)]="loginData.password"
                required
                minlength="6"
                #password="ngModel"
                placeholder="Votre mot de passe"
              >
              <button
                type="button"
                class="toggle-password"
                (click)="togglePasswordVisibility()"
              >
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
            <div class="error-message" *ngIf="password.invalid && (password.dirty || password.touched)">
              <span *ngIf="password.errors?.['required']">Le mot de passe est requis</span>
              <span *ngIf="password.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caractères</span>
            </div>
          </div>

          <div class="form-options">
            <div class="remember-me">
              <input
                type="checkbox"
                id="remember"
                name="remember"
                [(ngModel)]="loginData.rememberMe"
              >
              <label for="remember">Se souvenir de moi</label>
            </div>
            <a href="#" class="forgot-password">Mot de passe oublié?</a>
          </div>

          <div class="form-actions">
            <button
              type="submit"
              class="submit-btn"
              [disabled]="loginForm.invalid || isLoading"
            >
              <span *ngIf="!isLoading">Se connecter</span>
              <span *ngIf="isLoading">Connexion en cours...</span>
            </button>
          </div>
        </form>

        <div class="social-login">
          <p class="separator">Ou connectez-vous avec</p>
          <div class="social-buttons">
            <button class="social-btn google">
              <img src="assets/google-icon.png" alt="Google">
              Google
            </button>
            <button class="social-btn facebook">
              <span class="facebook-icon">f</span>
              Facebook
            </button>
          </div>
        </div>

        <div class="test-accounts">
          <h3>Comptes de test disponibles</h3>
          <p class="test-info">Cliquez sur un compte pour remplir automatiquement le formulaire :</p>
          <div class="test-accounts-grid">
            <div
              *ngFor="let account of staticAccounts"
              class="test-account-card"
              (click)="fillTestAccount(account)">
              <div class="account-info">
                <h4>{{ account.name }}</h4>
                <p class="email">{{ account.email }}</p>
                <p class="password">Mot de passe: {{ account.password }}</p>
                <span class="role-badge" [class]="'role-' + account.role">{{ account.role }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="auth-footer">
          <p>Vous n'avez pas de compte? <a routerLink="/signup">Inscrivez-vous</a></p>
        </div>
      </div>
    </div>

    <div class="navigation">
      <a routerLink="/accueil" class="back-btn">
        <span class="icon">←</span>
        <span>Retour à l'accueil</span>
      </a>
    </div>
  </div>
</div>
