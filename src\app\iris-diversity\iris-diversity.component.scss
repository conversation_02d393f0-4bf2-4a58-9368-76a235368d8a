.page-container.iris-diversity {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  font-family: 'Montserrat', sans-serif;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;

      .logo {
        font-family: 'Playfair Display', serif;
        font-size: 1.8rem;
        font-weight: 700;
        color: #333;
      }

      .nav-links {
        display: flex;
        list-style: none;
        gap: 30px;

        li {
          a {
            text-decoration: none;
            color: #555;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover, &.active {
              color: var(--fleur-primary);
            }
          }
        }
      }

      .auth-buttons {
        display: flex;
        gap: 15px;

        .login-button, .register-button {
          padding: 10px 25px;
          border-radius: 50px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          text-decoration: none;
        }

        .login-button {
          background-color: transparent;
          color: var(--fleur-primary);
          border: 1px solid var(--fleur-primary);

          &:hover {
            background-color: rgba(138, 79, 255, 0.1);
            transform: translateY(-3px);
          }
        }

        .register-button {
          background-color: var(--fleur-primary);
          color: white;
          border: 1px solid var(--fleur-primary);

          &:hover {
            background-color: darken(#8a4fff, 10%);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);
          }
        }
      }
    }

    .content {
      padding: 20px 0 60px;

      .main-card {
        background-color: white;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;

        .card-header {
          text-align: center;
          margin-bottom: 30px;

          h1 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
          }

          .divider {
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
            margin: 0 auto;
            border-radius: 3px;
          }
        }

        .iris-diversity-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 40px;

          .iris-circle-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin: 20px auto 40px;

            .iris-main-image {
              display: flex;
              justify-content: center;
              align-items: center;

              .diversity-image {
                width: 100%;
                max-width: 600px;
                height: auto;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;

                &:hover {
                  transform: scale(1.02);
                  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                }
              }
            }
          }

          .text-sections {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;

            .text-section {
              flex: 1;
              min-width: 300px;
              padding: 20px;
              background-color: rgba(245, 247, 250, 0.7);
              border-radius: 15px;
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);

              h3 {
                font-size: 1.3rem;
                color: #333;
                margin-bottom: 15px;
                font-weight: 600;
              }

              p {
                font-size: 1rem;
                line-height: 1.6;
                color: #555;
                text-align: justify;
              }

              &.left {
                border-left: 3px solid var(--fleur-primary);
              }

              &.right {
                border-left: 3px solid var(--bijou-primary);
              }
            }
          }
        }

        .navigation-buttons {
          display: flex;
          justify-content: space-between;
          margin-top: 30px;

          .nav-button {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 25px;
            background-color: white;
            color: #555;
            border-radius: 50px;
            font-weight: 500;
            text-decoration: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-3px);
              box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            }

            &.prev:hover {
              color: var(--fleur-primary);
            }

            &.next {
              background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
              color: white;
              box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);

              &:hover {
                box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);
              }
            }

            .icon {
              font-size: 1.1rem;
            }
          }
        }
      }
    }
  }
}

// Media queries pour la responsivité
@media (max-width: 768px) {
  .page-container.iris-diversity {
    .container {
      .navbar {
        flex-direction: column;
        gap: 15px;

        .nav-links {
          gap: 15px;
        }
      }

      .content {
        .main-card {
          padding: 25px;

          .card-header {
            h1 {
              font-size: 2rem;
            }
          }

          .iris-diversity-content {
            .iris-circle-container {
              width: 300px;
              height: 300px;

              .iris-circle {
                .iris-image {
                  width: 50px;
                  height: 50px;
                  margin: -25px;
                }
              }
            }

            .text-sections {
              flex-direction: column;
            }
          }

          .navigation-buttons {
            flex-direction: column;
            gap: 15px;

            .nav-button {
              width: 100%;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}