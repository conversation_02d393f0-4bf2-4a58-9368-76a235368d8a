<div class="admin-container">
  <div class="admin-header">
    <h1>🔧 Administration - Données Firebase</h1>
    <div class="admin-actions">
      <button class="btn btn-primary" (click)="initializeData()" [disabled]="isLoading">
        <span *ngIf="isLoading">⏳</span>
        <span *ngIf="!isLoading">🚀</span>
        Initialiser les données
      </button>
      <button class="btn btn-secondary" (click)="loadAllData()" [disabled]="isLoading">
        <span *ngIf="isLoading">⏳</span>
        <span *ngIf="!isLoading">🔄</span>
        Recharger
      </button>
      <button class="btn btn-success" (click)="exportData()">
        📥 Exporter
      </button>
    </div>
  </div>

  <!-- Navigation par onglets -->
  <div class="tabs">
    <button 
      class="tab-button" 
      [class.active]="selectedTab === 'families'"
      (click)="selectTab('families')">
      👥 Familles ({{families.length}})
    </button>
    <button 
      class="tab-button" 
      [class.active]="selectedTab === 'questions'"
      (click)="selectTab('questions')">
      ❓ Questions ({{questions.length}})
    </button>
    <button 
      class="tab-button" 
      [class.active]="selectedTab === 'responses'"
      (click)="selectTab('responses')">
      💬 Réponses ({{userResponses.length}})
    </button>
    <button 
      class="tab-button" 
      [class.active]="selectedTab === 'stats'"
      (click)="selectTab('stats')">
      📊 Statistiques ({{sessionStats.length}})
    </button>
    <button 
      class="tab-button" 
      [class.active]="selectedTab === 'tests'"
      (click)="selectTab('tests')">
      🧪 Tests ({{personalityTests.length}})
    </button>
  </div>

  <!-- Contenu des onglets -->
  <div class="tab-content">
    
    <!-- Onglet Familles -->
    <div *ngIf="selectedTab === 'families'" class="families-tab">
      <h2>🌟 Familles de Personnalité</h2>
      <div class="families-grid">
        <div *ngFor="let family of families" class="family-card">
          <div class="family-header">
            <h3>{{family.name}}</h3>
            <span class="family-id">ID: {{family.id}}</span>
          </div>
          <p class="family-description">{{family.description}}</p>
          <div class="family-characteristics">
            <span *ngFor="let char of family.characteristics" class="characteristic-tag">
              {{char}}
            </span>
          </div>
          <div class="family-stats">
            <span class="stat">📝 {{family.questions.length}} questions</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Questions -->
    <div *ngIf="selectedTab === 'questions'" class="questions-tab">
      <h2>❓ Questions du Test</h2>
      <div class="questions-table">
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Question</th>
              <th>Réponse Attendue</th>
              <th>Classes</th>
              <th>Poids</th>
              <th>Catégorie</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let question of questions">
              <td>{{question.id}}</td>
              <td class="question-text">{{question.question}}</td>
              <td>
                <span class="answer-badge" [class.yes]="question.expectedAnswer" [class.no]="!question.expectedAnswer">
                  {{question.expectedAnswer ? 'Oui' : 'Non'}}
                </span>
              </td>
              <td>
                <span *ngFor="let cls of question.classes" class="class-tag">{{cls}}</span>
              </td>
              <td>{{question.weight}}</td>
              <td>
                <span class="category-tag">{{question.category}}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Onglet Réponses -->
    <div *ngIf="selectedTab === 'responses'" class="responses-tab">
      <h2>💬 Réponses des Utilisateurs</h2>
      <div class="responses-table">
        <table>
          <thead>
            <tr>
              <th>Utilisateur</th>
              <th>Session</th>
              <th>Question</th>
              <th>Réponse</th>
              <th>Temps de Réponse</th>
              <th>Timestamp</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let response of userResponses | slice:0:100">
              <td class="user-id">{{response.userId}}</td>
              <td class="session-id">{{response.sessionId}}</td>
              <td>{{response.questionId}}</td>
              <td>
                <span class="answer-badge" [class.yes]="response.answer" [class.no]="!response.answer">
                  {{response.answer ? 'Oui' : 'Non'}}
                </span>
              </td>
              <td>{{response.responseTime || 0}}ms</td>
              <td>{{formatDate(response.timestamp)}}</td>
            </tr>
          </tbody>
        </table>
        <p *ngIf="userResponses.length > 100" class="table-note">
          Affichage des 100 premières réponses sur {{userResponses.length}} total.
        </p>
      </div>
    </div>

    <!-- Onglet Statistiques -->
    <div *ngIf="selectedTab === 'stats'" class="stats-tab">
      <h2>📊 Statistiques de Session</h2>
      <div class="stats-grid">
        <div *ngFor="let stat of sessionStats" class="stat-card">
          <div class="stat-header">
            <h4>Session: {{stat.sessionId}}</h4>
            <span class="stat-date">{{formatDate(stat.completedAt)}}</span>
          </div>
          <div class="stat-details">
            <div class="stat-item">
              <span class="label">Utilisateur:</span>
              <span class="value">{{stat.userId}}</span>
            </div>
            <div class="stat-item">
              <span class="label">Questions:</span>
              <span class="value">{{stat.totalResponses}}/{{stat.totalQuestions}}</span>
            </div>
            <div class="stat-item">
              <span class="label">Taux de completion:</span>
              <span class="value">{{stat.completionRate}}%</span>
            </div>
            <div class="stat-item">
              <span class="label">Temps moyen:</span>
              <span class="value">{{stat.averageResponseTime}}ms</span>
            </div>
            <div class="stat-item" *ngIf="stat.profile">
              <span class="label">Profil:</span>
              <span class="value profile-badge">{{stat.profile.primaryClass}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Tests -->
    <div *ngIf="selectedTab === 'tests'" class="tests-tab">
      <h2>🧪 Tests de Personnalité Complets</h2>
      <div class="tests-table">
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Utilisateur</th>
              <th>Profil Principal</th>
              <th>Profil Secondaire</th>
              <th>Démarré</th>
              <th>Terminé</th>
              <th>Réponses</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let test of personalityTests">
              <td class="test-id">{{test.id}}</td>
              <td>{{test.userId}}</td>
              <td>
                <span class="profile-badge primary" *ngIf="test.finalProfile">
                  {{test.finalProfile.primaryClass}}
                </span>
              </td>
              <td>
                <span class="profile-badge secondary" *ngIf="test.finalProfile">
                  {{test.finalProfile.secondaryClass}}
                </span>
              </td>
              <td>{{formatDate(test.startedAt)}}</td>
              <td>{{formatDate(test.completedAt)}}</td>
              <td>{{test.responses?.length || 0}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

  </div>

  <!-- Indicateur de chargement -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Chargement des données...</p>
    </div>
  </div>
</div>
