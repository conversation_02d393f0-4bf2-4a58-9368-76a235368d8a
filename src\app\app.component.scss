* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  :host {
    display: block;
    font-family: 'Raleway', sans-serif;
    color: #1a1a1a;
    min-height: 100vh;
  }

  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  router-outlet + * {
    flex: 1 0 auto;
  }

  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 4rem;

    .logo {
      font-family: 'Raleway', cursive;
      font-weight: bold;
      font-size: 1.8rem;
    }

    nav {
      display: flex;
      gap: 2rem;

      a {
        text-decoration: none;
        color: #1a1a1a;
        font-size: 1.1rem;

        &.active {
          font-weight: bold;
        }
      }
    }

    .register-btn {
      padding: 0.5rem 1.5rem;
      border: 2px solid #1a1a1a;
      background: transparent;
      border-radius: 999px;
      font-size: 1rem;
      cursor: pointer;
    }
  }

  .hero {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4rem;

    .hero-text {
      flex: 1;

      h1 {
        font-size: 3rem;
        margin-bottom: 1.5rem;
      }

      p {
        font-size: 1.2rem;
        line-height: 1.6;
        font-style: italic;
        margin-bottom: 2rem;
      }

      .start-btn {
        padding: 0.75rem 1.5rem;
        border: 2px solid #1a1a1a;
        background: transparent;
        border-radius: 999px;
        font-size: 1rem;
        cursor: pointer;
      }
    }

    .hero-image {
      flex: 1;
      display: flex;
      justify-content: center;

      img {
        max-width: 100%;
        height: auto;
      }
    }
  }
