import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PersonalityTestService } from '../services/personality-test.service';
import { IrisCompatibilityService, CompatibilityResult } from '../services/iris-compatibility.service';
import {
  Question,
  UserResponse,
  TestSession,
  PersonalityProfile,
  PERSONALITY_QUESTIONS
} from '../models/personality-test.model';

@Component({
  selector: 'app-personality-test',
  templateUrl: './personality-test.component.html',
  styleUrls: ['./personality-test.component.scss']
})
export class PersonalityTestComponent implements OnInit {
  // Données du test
  questions: Question[] = PERSONALITY_QUESTIONS;
  currentQuestionIndex: number = 0;
  responses: UserResponse[] = [];
  testSession: TestSession | null = null;

  // État du composant
  isTestStarted: boolean = false;
  isTestCompleted: boolean = false;
  isLoading: boolean = false;
  showResults: boolean = false;
  currentSessionId: string = '';

  // Comptes statiques pour les tests
  staticUsers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      description: 'Profil créatif et émotionnel'
    },
    {
      id: 2,
      name: 'Jean Martin',
      email: '<EMAIL>',
      description: 'Profil analytique et structuré'
    },
    {
      id: 3,
      name: 'Sophie Leroy',
      email: '<EMAIL>',
      description: 'Profil dynamique et aventurier'
    },
    {
      id: 4,
      name: 'Pierre Moreau',
      email: '<EMAIL>',
      description: 'Profil paisible et réfléchi'
    },
    {
      id: 5,
      name: 'Emma Bernard',
      email: '<EMAIL>',
      description: 'Profil mixte créatif-analytique'
    }
  ];

  selectedUser = this.staticUsers[0]; // Utilisateur par défaut
  currentUser: any = null; // Utilisateur connecté

  // Résultats
  finalProfile: PersonalityProfile | null = null;
  compatibilityResult: CompatibilityResult | null = null;

  constructor(
    private personalityTestService: PersonalityTestService,
    private irisCompatibilityService: IrisCompatibilityService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Récupérer l'utilisateur connecté
    const currentUserData = localStorage.getItem('currentUser');
    if (currentUserData) {
      this.currentUser = JSON.parse(currentUserData);
      // Utiliser l'utilisateur connecté pour le test
      this.selectedUser = {
        id: 0,
        name: this.currentUser.name,
        email: this.currentUser.email,
        description: 'Utilisateur connecté'
      };
    }
    this.initializeTest();
  }

  /**
   * Initialise le test
   */
  initializeTest(): void {
    this.testSession = this.personalityTestService.createTestSession(
      this.selectedUser.name,
      this.selectedUser.email
    );
  }

  /**
   * Sélectionne un utilisateur de test
   */
  selectUser(user: any): void {
    this.selectedUser = user;
    this.initializeTest();
  }

  /**
   * Démarre le test
   */
  startTest(): void {
    this.isTestStarted = true;
    this.currentQuestionIndex = 0;
    this.responses = [];
    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    if (this.testSession) {
      this.testSession.startedAt = new Date();
      this.testSession.id = this.currentSessionId;
    }

    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);
  }

  /**
   * Traite la réponse à une question
   */
  answerQuestion(answer: boolean): void {
    if (!this.testSession) return;

    const currentQuestion = this.questions[this.currentQuestionIndex];
    const response: UserResponse = {
      questionId: currentQuestion.id,
      answer: answer,
      timestamp: new Date()
    };

    this.responses.push(response);
    this.testSession.responses = this.responses;

    // Sauvegarder la réponse individuelle en temps réel
    if (this.currentUser && this.currentSessionId) {
      this.personalityTestService.saveIndividualResponse(
        this.currentUser.email,
        this.currentSessionId,
        response
      ).subscribe({
        next: (success) => {
          if (success) {
            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);
          } else {
            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);
          }
        },
        error: (error) => {
          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);
        }
      });
    }

    // Passer à la question suivante ou terminer le test
    if (this.currentQuestionIndex < this.questions.length - 1) {
      this.currentQuestionIndex++;
    } else {
      this.completeTest();
    }
  }

  /**
   * Termine le test et calcule les résultats
   */
  completeTest(): void {
    if (!this.testSession) return;

    this.isLoading = true;
    console.log('🔄 Début de l\'analyse des résultats...');

    // Simuler un délai d'analyse réaliste (2-3 secondes)
    setTimeout(() => {
      this.processTestResults();
    }, 2500);
  }

  /**
   * Traite les résultats du test
   */
  private processTestResults(): void {
    if (!this.testSession) return;

    try {
      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');

      // Calculer les résultats
      const results = this.personalityTestService.processTestResults(this.responses);
      console.log('✅ Résultats calculés:', results);

      // Mettre à jour la session
      this.testSession.scores = results.scores;
      this.testSession.finalProfile = results.profile;
      this.testSession.completedAt = new Date();

      this.finalProfile = results.profile;

      // Vérifier la compatibilité avec l'iris
      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(
        results.profile,
        this.selectedUser.email
      );
      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);

      // Sauvegarder les statistiques de session
      const sessionStats = {
        totalQuestions: this.questions.length,
        totalResponses: this.responses.length,
        completionRate: (this.responses.length / this.questions.length) * 100,
        averageResponseTime: this.calculateAverageResponseTime(),
        scores: results.scores,
        profile: results.profile,
        userId: this.currentUser?.email,
        completedAt: new Date().toISOString()
      };

      // Sauvegarder les statistiques
      if (this.currentSessionId) {
        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({
          next: (success) => {
            console.log('📊 Statistiques de session sauvegardées:', success);
          },
          error: (error) => {
            console.error('❌ Erreur sauvegarde stats:', error);
          }
        });
      }

      // Sauvegarder dans Firebase
      this.personalityTestService.saveTestSession(this.testSession).subscribe({
        next: (sessionId) => {
          console.log('✅ Test sauvegardé avec l\'ID:', sessionId);
          this.testSession!.id = sessionId;
          this.showResultsWithDelay();
        },
        error: (error) => {
          console.error('❌ Erreur lors de la sauvegarde:', error);
          this.showResultsWithDelay();
        }
      });

    } catch (error) {
      console.error('❌ Erreur lors du traitement des résultats:', error);
      this.showResultsWithDelay();
    }
  }

  /**
   * Affiche les résultats avec un délai pour une transition fluide
   */
  private showResultsWithDelay(): void {
    setTimeout(() => {
      this.isLoading = false;
      this.isTestCompleted = true;
      this.showResults = true;
      console.log('🎉 Résultats affichés !');
    }, 500);
  }

  /**
   * Calcule le temps de réponse moyen
   */
  private calculateAverageResponseTime(): number {
    if (this.responses.length === 0) return 0;

    let totalTime = 0;
    for (let i = 1; i < this.responses.length; i++) {
      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i-1].timestamp.getTime();
      totalTime += timeDiff;
    }

    return totalTime / (this.responses.length - 1);
  }

  /**
   * Redémarre le test
   */
  restartTest(): void {
    this.isTestStarted = false;
    this.isTestCompleted = false;
    this.showResults = false;
    this.currentQuestionIndex = 0;
    this.responses = [];
    this.finalProfile = null;
    this.compatibilityResult = null;
    this.currentSessionId = '';
    this.initializeTest();
  }

  /**
   * Retourne à l'accueil
   */
  goToHome(): void {
    this.router.navigate(['/accueil']);
  }

  /**
   * Déconnexion de l'utilisateur
   */
  logout(): void {
    localStorage.removeItem('currentUser');
    this.router.navigate(['/login']);
  }

  /**
   * Obtient la question actuelle
   */
  get currentQuestion(): Question | null {
    return this.questions[this.currentQuestionIndex] || null;
  }

  /**
   * Obtient le pourcentage de progression
   */
  get progressPercentage(): number {
    return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
  }

  /**
   * Obtient le numéro de la question actuelle
   */
  get currentQuestionNumber(): number {
    return this.currentQuestionIndex + 1;
  }

  /**
   * Obtient le nombre total de questions
   */
  get totalQuestions(): number {
    return this.questions.length;
  }

  /**
   * Vérifie si un score est le plus élevé
   */
  isHighestScore(family: string): boolean {
    if (!this.testSession?.scores) return false;

    const scores = this.testSession.scores;
    const currentScore = scores[family as keyof typeof scores] as number;
    const maxScore = Math.max(scores.flower, scores.jewel, scores.shaker, scores.stream);

    return currentScore === maxScore && maxScore > 0;
  }

  /**
   * Calcule le pourcentage d'un score
   */
  getScorePercentage(family: string): number {
    if (!this.testSession?.scores) return 0;

    const scores = this.testSession.scores;
    const score = scores[family as keyof typeof scores] as number;

    return Math.round((score / 4) * 100);
  }

  /**
   * Obtient la famille dominante
   */
  getDominantFamily(): string {
    if (!this.testSession?.scores) return 'Aucune';

    const scores = this.testSession.scores;
    const families = [
      { name: '🌸 Flower', score: scores.flower },
      { name: '💎 Jewel', score: scores.jewel },
      { name: '⚡ Shaker', score: scores.shaker },
      { name: '🌊 Stream', score: scores.stream }
    ];

    const dominant = families.reduce((prev, current) =>
      current.score > prev.score ? current : prev
    );

    return dominant.name;
  }
}
