import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PersonalityTestService } from '../services/personality-test.service';
import { IrisCompatibilityService, CompatibilityResult } from '../services/iris-compatibility.service';
import {
  Question,
  UserResponse,
  TestSession,
  PersonalityProfile,
  PERSONALITY_QUESTIONS
} from '../models/personality-test.model';

@Component({
  selector: 'app-personality-test',
  templateUrl: './personality-test.component.html',
  styleUrls: ['./personality-test.component.scss']
})
export class PersonalityTestComponent implements OnInit {
  // Données du test
  questions: Question[] = PERSONALITY_QUESTIONS;
  currentQuestionIndex: number = 0;
  responses: UserResponse[] = [];
  testSession: TestSession | null = null;

  // État du composant
  isTestStarted: boolean = false;
  isTestCompleted: boolean = false;
  isLoading: boolean = false;
  showResults: boolean = false;

  // Comptes statiques pour les tests
  staticUsers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      description: 'Profil créatif et émotionnel'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      description: 'Profil analytique et structuré'
    },
    {
      id: 3,
      name: 'Sophie Leroy',
      email: '<EMAIL>',
      description: 'Profil dynamique et aventurier'
    },
    {
      id: 4,
      name: 'Pierre Moreau',
      email: '<EMAIL>',
      description: 'Profil paisible et réfléchi'
    },
    {
      id: 5,
      name: 'Emma Bernard',
      email: '<EMAIL>',
      description: 'Profil mixte créatif-analytique'
    }
  ];

  selectedUser = this.staticUsers[0]; // Utilisateur par défaut
  currentUser: any = null; // Utilisateur connecté

  // Résultats
  finalProfile: PersonalityProfile | null = null;
  compatibilityResult: CompatibilityResult | null = null;

  constructor(
    private personalityTestService: PersonalityTestService,
    private irisCompatibilityService: IrisCompatibilityService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Récupérer l'utilisateur connecté
    const currentUserData = localStorage.getItem('currentUser');
    if (currentUserData) {
      this.currentUser = JSON.parse(currentUserData);
      // Utiliser l'utilisateur connecté pour le test
      this.selectedUser = {
        id: 0,
        name: this.currentUser.name,
        email: this.currentUser.email,
        description: 'Utilisateur connecté'
      };
    }
    this.initializeTest();
  }

  /**
   * Initialise le test
   */
  initializeTest(): void {
    this.testSession = this.personalityTestService.createTestSession(
      this.selectedUser.name,
      this.selectedUser.email
    );
  }

  /**
   * Sélectionne un utilisateur de test
   */
  selectUser(user: any): void {
    this.selectedUser = user;
    this.initializeTest();
  }

  /**
   * Démarre le test
   */
  startTest(): void {
    this.isTestStarted = true;
    this.currentQuestionIndex = 0;
    this.responses = [];
    if (this.testSession) {
      this.testSession.startedAt = new Date();
    }
  }

  /**
   * Traite la réponse à une question
   */
  answerQuestion(answer: boolean): void {
    if (!this.testSession) return;

    const currentQuestion = this.questions[this.currentQuestionIndex];
    const response: UserResponse = {
      questionId: currentQuestion.id,
      answer: answer,
      timestamp: new Date()
    };

    this.responses.push(response);
    this.testSession.responses = this.responses;

    // Passer à la question suivante ou terminer le test
    if (this.currentQuestionIndex < this.questions.length - 1) {
      this.currentQuestionIndex++;
    } else {
      this.completeTest();
    }
  }

  /**
   * Termine le test et calcule les résultats
   */
  completeTest(): void {
    if (!this.testSession) return;

    this.isLoading = true;

    // Calculer les résultats
    const results = this.personalityTestService.processTestResults(this.responses);

    // Mettre à jour la session
    this.testSession.scores = results.scores;
    this.testSession.finalProfile = results.profile;
    this.testSession.completedAt = new Date();

    this.finalProfile = results.profile;

    // Vérifier la compatibilité avec l'iris
    this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(
      results.profile,
      this.selectedUser.email
    );

    // Sauvegarder dans Firebase
    this.personalityTestService.saveTestSession(this.testSession).subscribe({
      next: (sessionId) => {
        console.log('Test sauvegardé avec l\'ID:', sessionId);
        this.testSession!.id = sessionId;
        this.isLoading = false;
        this.isTestCompleted = true;
        this.showResults = true;
      },
      error: (error) => {
        console.error('Erreur lors de la sauvegarde:', error);
        this.isLoading = false;
        this.isTestCompleted = true;
        this.showResults = true;
      }
    });
  }

  /**
   * Redémarre le test
   */
  restartTest(): void {
    this.isTestStarted = false;
    this.isTestCompleted = false;
    this.showResults = false;
    this.currentQuestionIndex = 0;
    this.responses = [];
    this.finalProfile = null;
    this.compatibilityResult = null;
    this.initializeTest();
  }

  /**
   * Retourne à l'accueil
   */
  goToHome(): void {
    this.router.navigate(['/accueil']);
  }

  /**
   * Déconnexion de l'utilisateur
   */
  logout(): void {
    localStorage.removeItem('currentUser');
    this.router.navigate(['/login']);
  }

  /**
   * Obtient la question actuelle
   */
  get currentQuestion(): Question | null {
    return this.questions[this.currentQuestionIndex] || null;
  }

  /**
   * Obtient le pourcentage de progression
   */
  get progressPercentage(): number {
    return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
  }

  /**
   * Obtient le numéro de la question actuelle
   */
  get currentQuestionNumber(): number {
    return this.currentQuestionIndex + 1;
  }

  /**
   * Obtient le nombre total de questions
   */
  get totalQuestions(): number {
    return this.questions.length;
  }
}
