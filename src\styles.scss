/* Styles globaux pour l'application */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Montserrat', sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
}

a {
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
}

/* Variables de couleurs pour les différents types d'iris - Uniformisées en tons de bleu */
:root {
  --fleur-primary: #6a5acd;
  --fleur-secondary: #a29bfe;
  --fleur-gradient: linear-gradient(135deg, #f0f2ff 0%, #e6e9ff 100%);

  --bijou-primary: #4f8aff;
  --bijou-secondary: #92b8ff;
  --bijou-gradient: linear-gradient(135deg, #e6f0ff 0%, #e0e6ff 100%);

  --flux-primary: #3498db;
  --flux-secondary: #74b9ff;
  --flux-gradient: linear-gradient(135deg, #e6f7ff 0%, #e0f0ff 100%);

  --shaker-primary: #0984e3;
  --shaker-secondary: #64b5f6;
  --shaker-gradient: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

/* Classes utilitaires */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 50px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
