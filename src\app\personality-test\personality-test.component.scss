// Variables de couleurs - Design professionnel épuré
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --accent-color: #0f172a;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --text-primary: #0f172a;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --background-light: #f8fafc;
  --background-white: #ffffff;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

.personality-test-container {
  min-height: 100vh;
  background: var(--background-light);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

// Styles communs pour les cartes
.intro-card, .test-card, .results-card, .loading-card {
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  padding: 3rem;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

// Styles communs pour les titres et diviseurs
.title {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 400;
}

.divider {
  width: 60px;
  height: 3px;
  background: var(--primary-color);
  margin: 0 auto 2rem;
  border-radius: 2px;
}

// Styles pour les boutons
.btn {
  padding: 0.875rem 1.75rem;
  border: 1px solid transparent;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-family: inherit;

  &.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);

    &:hover {
      background: #1d4ed8;
      border-color: #1d4ed8;
      box-shadow: var(--shadow-md);
    }
  }

  &.btn-secondary {
    background: var(--background-white);
    color: var(--text-secondary);
    border-color: var(--border-color);

    &:hover {
      background: var(--background-light);
      border-color: var(--secondary-color);
      color: var(--text-primary);
    }
  }

  &.btn-logout {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);

    &:hover {
      background: #b91c1c;
      border-color: #b91c1c;
      box-shadow: var(--shadow-md);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
}

// Page d'introduction
.test-intro {
  .intro-content {
    margin: 2rem 0;
  }

  .test-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--background-light);
    border: 1px solid var(--border-light);
    border-radius: 12px;

    .icon {
      font-size: 1.5rem;
      color: var(--primary-color);
    }

    .info-text {
      h3 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
      }
    }
  }

  .user-info {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    border-radius: 12px;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--text-primary);
      font-weight: 600;
    }

    p {
      margin: 0.5rem 0;
      color: var(--text-secondary);

      &.note {
        font-style: italic;
        color: var(--text-muted);
        font-size: 0.875rem;
      }
    }
  }

  .intro-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Interface du test
.test-interface {
  .progress-section {
    margin-bottom: 3rem;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .question-counter {
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .progress-percentage {
      font-weight: 600;
      color: var(--primary-color);
      font-size: 0.875rem;
    }
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: var(--border-light);
    border-radius: 3px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
  }

  .question-section {
    text-align: center;
  }

  .question-content {
    margin-bottom: 3rem;

    .question-text {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
      line-height: 1.5;
      margin: 0;
      letter-spacing: -0.025em;
    }
  }

  .answer-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
  }

  .btn-answer {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-width: 120px;
    border-radius: 8px;
    font-weight: 500;

    .answer-icon {
      font-size: 1.125rem;
    }

    &.btn-yes {
      background: var(--success-color);
      color: white;
      border-color: var(--success-color);

      &:hover {
        background: #047857;
        border-color: #047857;
        box-shadow: var(--shadow-md);
      }
    }

    &.btn-no {
      background: var(--danger-color);
      color: white;
      border-color: var(--danger-color);

      &:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        box-shadow: var(--shadow-md);
      }
    }
  }
}

// Écran de chargement
.loading-screen {
  .loading-card {
    text-align: center;

    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 3px solid var(--border-light);
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 2rem;
    }

    h2 {
      color: var(--text-primary);
      margin-bottom: 1rem;
      font-weight: 600;
    }

    p {
      color: var(--text-secondary);
      margin: 0 0 1.5rem 0;
    }

    .loading-steps {
      text-align: left;
      margin-top: 1.5rem;

      .step {
        padding: 0.5rem 0;
        color: var(--primary-color);
        font-size: 0.875rem;
        opacity: 0.7;
        transition: opacity 0.3s ease;

        &.active {
          opacity: 1;
          font-weight: 500;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Résultats du test
.test-results {
  .profile-summary {
    text-align: center;
    margin: 2rem 0;
  }

  .profile-badge {
    display: inline-block;
    padding: 2rem;
    border-radius: 16px;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);

    &.profile-flower {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-jewel {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-shaker {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-stream {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-flowerjewel {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-jewelshaker {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-shakerstream {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    &.profile-streamflower {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: var(--text-primary);
    }

    .profile-name {
      color: var(--text-primary);
      font-size: 1.875rem;
      font-weight: 700;
      margin: 0 0 1rem 0;
      letter-spacing: -0.025em;
    }

    .confidence-score {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .score-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
      }

      .score-value {
        color: var(--primary-color);
        font-size: 1.25rem;
        font-weight: 700;
      }
    }
  }

  .profile-type {
    margin-bottom: 2rem;

    .type-badge {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      border: 1px solid var(--border-color);

      &.primary {
        background: var(--background-light);
        color: var(--primary-color);
        border-color: var(--primary-color);
      }

      &.intermediate {
        background: var(--background-light);
        color: var(--secondary-color);
        border-color: var(--secondary-color);
      }
    }
  }

  .profile-description {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;

    h3 {
      color: var(--text-primary);
      margin: 0 0 1rem 0;
      font-weight: 600;
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
      margin: 0;
      font-size: 1rem;
    }
  }

  .iris-compatibility {
    margin: 2rem 0;

    h3 {
      color: #2d3748;
      margin: 0 0 1.5rem 0;
      text-align: center;
    }

    .compatibility-summary {
      padding: 2rem;
      border-radius: 12px;
      margin-bottom: 2rem;

      &.compatible {
        background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
        border: 2px solid rgba(72, 187, 120, 0.3);
      }

      &.incompatible {
        background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);
        border: 2px solid rgba(245, 101, 101, 0.3);
      }

      .compatibility-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
        }

        .iris-info {
          flex: 1;

          h4 {
            color: #2d3748;
            margin: 0 0 0.5rem 0;
            font-size: 1.2rem;
          }

          p {
            color: #4a5568;
            margin: 0;
            font-size: 0.9rem;
          }
        }

        .compatibility-score {
          .score-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &.high-score {
              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            }

            &.low-score {
              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            }

            .score-value {
              color: white;
              font-size: 1.5rem;
              font-weight: 700;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .score-label {
              color: rgba(255, 255, 255, 0.9);
              font-size: 0.7rem;
              font-weight: 500;
            }
          }
        }
      }

      .compatibility-status {
        text-align: center;

        .status-badge {
          display: inline-block;
          padding: 0.5rem 1.5rem;
          border-radius: 20px;
          font-weight: 600;
          font-size: 1rem;

          &.compatible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
          }

          &.incompatible {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
          }
        }
      }
    }

    .iris-characteristics {
      background: rgba(118, 75, 162, 0.1);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      .characteristics-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .characteristic-tag {
          display: inline-block;
          padding: 0.3rem 0.8rem;
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
          color: white;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 500;
        }
      }
    }

    .recommendations, .corrections {
      background: rgba(255, 255, 255, 0.5);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0.5rem;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .corrections {
      border-left: 4px solid #f56565;

      h4 {
        color: #e53e3e;
      }
    }
  }

  .detailed-scores {
    margin: 2rem 0;
    background: rgba(255, 255, 255, 0.8);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    h3 {
      color: #2d3748;
      margin: 0 0 0.5rem 0;
      font-size: 1.4rem;
      text-align: center;
    }

    .scores-description {
      color: #718096;
      text-align: center;
      margin-bottom: 2rem;
      font-size: 1rem;
    }

    .scores-grid {
      display: grid;
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .score-item {
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      border: 2px solid transparent;
      transition: all 0.3s ease;

      &.highest-score {
        border-color: #667eea;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
      }

      .score-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .score-label {
          font-weight: 700;
          color: #2d3748;
          font-size: 1.1rem;
        }

        .score-percentage {
          font-weight: 700;
          color: #667eea;
          font-size: 1.2rem;
        }
      }

      .score-bar {
        height: 12px;
        background: #e2e8f0;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 1rem;

        .score-fill {
          height: 100%;
          transition: width 1s ease;

          &.flower {
            background: linear-gradient(90deg, #fbb6ce, #f687b3);
          }

          &.jewel {
            background: linear-gradient(90deg, #90cdf4, #63b3ed);
          }

          &.shaker {
            background: linear-gradient(90deg, #fbd38d, #f6ad55);
          }

          &.stream {
            background: linear-gradient(90deg, #9ae6b4, #68d391);
          }
        }
      }

      .score-details {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .score-value {
          font-weight: 600;
          color: #2d3748;
          font-size: 1rem;
        }

        .score-description {
          color: #718096;
          font-size: 0.9rem;
          font-style: italic;
        }
      }
    }

    .score-summary {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      padding: 1.5rem;
      border-radius: 12px;
      border: 1px solid rgba(102, 126, 234, 0.2);

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 600;
          color: #4a5568;
          font-size: 1rem;
        }

        .value {
          font-weight: 700;
          font-size: 1.1rem;

          &.dominant {
            color: #667eea;
          }

          &.confidence {
            color: #38a169;
          }
        }
      }
    }
  }

  .test-info-summary {
    background: rgba(118, 75, 162, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    margin: 2rem 0;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #4a5568;
      }

      .value {
        color: #2d3748;
        font-family: 'Courier New', monospace;
      }
    }
  }

  .results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .personality-test-container {
    padding: 1rem;
  }

  .intro-card, .test-card, .results-card, .loading-card {
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
  }

  .question-text {
    font-size: 1.4rem !important;
  }

  .btn-answer {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-width: 120px !important;
  }

  .profile-name {
    font-size: 1.5rem !important;
  }
}