// Variables de couleurs - Design professionnel compatible avec le site
:root {
  // Couleurs principales du site
  --fleur-primary: #6a5acd;
  --bijou-primary: #4f8aff;
  --flux-primary: #3498db;
  --shaker-primary: #0984e3;

  // Palette professionnelle harmonisée
  --primary-color: #6a5acd;
  --secondary-color: #4f8aff;
  --accent-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;

  // Textes
  --text-primary: #333;
  --text-secondary: #555;
  --text-muted: #666;

  // Arrière-plans
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-tertiary: #e4e8f0;

  // Bordures et ombres
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 8px 30px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.personality-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Montserrat', sans-serif;
}

// Styles communs pour les cartes
.intro-card, .test-card, .results-card, .loading-card {
  background: var(--background-primary);
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
  padding: 3rem;
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
  }
}

// Styles communs pour les titres et diviseurs
.title {
  font-family: 'Playfair Display', serif;
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
}

.subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 400;
  line-height: 1.6;
}

.divider {
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
  margin: 0 auto 2rem;
  border-radius: 3px;
}

// Styles pour les boutons
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-family: 'Montserrat', sans-serif;

  &.btn-primary {
    background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
    color: white;
    box-shadow: 0 10px 25px rgba(106, 90, 205, 0.3);

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(106, 90, 205, 0.4);
    }
  }

  &.btn-secondary {
    background: var(--background-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);

    &:hover {
      background: var(--background-secondary);
      transform: translateY(-3px);
      box-shadow: var(--shadow-md);
    }
  }

  &.btn-logout {
    background: var(--danger-color);
    color: white;
    box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);

    &:hover {
      background: #c0392b;
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(231, 76, 60, 0.4);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
}

// Page d'introduction
.test-intro {
  .intro-content {
    margin: 2rem 0;
  }

  .test-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);

    &:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-md);
    }

    .icon {
      font-size: 1.5rem;
      color: var(--fleur-primary);
    }

    .info-text {
      h3 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 600;
        font-family: 'Playfair Display', serif;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
      }
    }
  }

  .user-info {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow-sm);

    h3 {
      margin: 0 0 1rem 0;
      color: var(--text-primary);
      font-weight: 600;
      font-family: 'Playfair Display', serif;
    }

    p {
      margin: 0.5rem 0;
      color: var(--text-secondary);
      line-height: 1.5;

      &.note {
        font-style: italic;
        color: var(--text-muted);
        font-size: 0.875rem;
      }
    }
  }

  .intro-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Interface du test
.test-interface {
  .progress-section {
    margin-bottom: 3rem;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);

    .question-counter {
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .progress-percentage {
      font-weight: 600;
      color: var(--fleur-primary);
      font-size: 0.875rem;
      background: var(--background-secondary);
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
    }
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 2rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
    transition: width 0.4s ease;
  }

  .question-section {
    text-align: center;
  }

  .question-content {
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--background-secondary);
    border-radius: 15px;
    border: 1px solid var(--border-color);

    .question-text {
      font-family: 'Playfair Display', serif;
      font-size: 1.4rem;
      font-weight: 500;
      color: var(--text-primary);
      line-height: 1.6;
      margin: 0;
      letter-spacing: -0.02em;
    }
  }

  .answer-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
  }

  .btn-answer {
    padding: 15px 35px;
    font-size: 1.1rem;
    min-width: 140px;
    border-radius: 50px;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;

    .answer-icon {
      font-size: 1.2rem;
    }

    &.btn-yes {
      background: var(--success-color);
      color: white;
      box-shadow: 0 10px 25px rgba(39, 174, 96, 0.3);

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(39, 174, 96, 0.4);
        background: #229954;
      }
    }

    &.btn-no {
      background: var(--danger-color);
      color: white;
      box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(231, 76, 60, 0.4);
        background: #c0392b;
      }
    }
  }
}

// Écran de chargement
.loading-screen {
  .loading-card {
    text-align: center;

    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 3px solid var(--background-tertiary);
      border-top: 3px solid var(--fleur-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 2rem;
    }

    h2 {
      color: var(--text-primary);
      margin-bottom: 1rem;
      font-weight: 600;
      font-family: 'Playfair Display', serif;
    }

    p {
      color: var(--text-secondary);
      margin: 0 0 1.5rem 0;
      line-height: 1.5;
    }

    .loading-steps {
      text-align: left;
      margin-top: 1.5rem;
      background: var(--background-secondary);
      padding: 1.5rem;
      border-radius: 15px;
      border: 1px solid var(--border-color);

      .step {
        padding: 0.5rem 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
        opacity: 0.7;
        transition: all 0.3s ease;

        &.active {
          opacity: 1;
          font-weight: 500;
          color: var(--fleur-primary);
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Résultats du test
.test-results {
  .profile-summary {
    text-align: center;
    margin: 2rem 0;
  }

  .profile-badge {
    display: inline-block;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    // Utilisation des couleurs du site pour chaque profil
    &.profile-flower {
      background: linear-gradient(135deg, var(--fleur-primary) 0%, var(--fleur-secondary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-jewel {
      background: linear-gradient(135deg, var(--bijou-primary) 0%, var(--bijou-secondary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-shaker {
      background: linear-gradient(135deg, var(--shaker-primary) 0%, var(--shaker-secondary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-stream {
      background: linear-gradient(135deg, var(--flux-primary) 0%, var(--flux-secondary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-flowerjewel {
      background: linear-gradient(135deg, var(--fleur-primary) 0%, var(--bijou-primary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-jewelshaker {
      background: linear-gradient(135deg, var(--bijou-primary) 0%, var(--shaker-primary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-shakerstream {
      background: linear-gradient(135deg, var(--shaker-primary) 0%, var(--flux-primary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    &.profile-streamflower {
      background: linear-gradient(135deg, var(--flux-primary) 0%, var(--fleur-primary) 100%);
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .profile-name {
      color: white;
      font-family: 'Playfair Display', serif;
      font-size: 1.875rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      letter-spacing: -0.025em;
    }

    .confidence-score {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .score-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.875rem;
        font-weight: 500;
      }

      .score-value {
        color: white;
        font-size: 1.25rem;
        font-weight: 700;
      }
    }
  }

  .profile-type {
    margin-bottom: 2rem;

    .type-badge {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;

      &.primary {
        background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
        color: white;
        box-shadow: 0 4px 15px rgba(106, 90, 205, 0.3);
      }

      &.intermediate {
        background: linear-gradient(to right, var(--bijou-primary), var(--flux-primary));
        color: white;
        box-shadow: 0 4px 15px rgba(79, 138, 255, 0.3);
      }
    }
  }

  .profile-description {
    background: var(--background-primary);
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);

    h3 {
      color: var(--text-primary);
      margin: 0 0 1rem 0;
      font-weight: 600;
      font-family: 'Playfair Display', serif;
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
      margin: 0;
      font-size: 1rem;
    }
  }

  .iris-compatibility {
    margin: 2rem 0;

    h3 {
      color: #2d3748;
      margin: 0 0 1.5rem 0;
      text-align: center;
    }

    .compatibility-summary {
      padding: 2rem;
      border-radius: 12px;
      margin-bottom: 2rem;

      &.compatible {
        background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
        border: 2px solid rgba(72, 187, 120, 0.3);
      }

      &.incompatible {
        background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);
        border: 2px solid rgba(245, 101, 101, 0.3);
      }

      .compatibility-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
        }

        .iris-info {
          flex: 1;

          h4 {
            color: #2d3748;
            margin: 0 0 0.5rem 0;
            font-size: 1.2rem;
          }

          p {
            color: #4a5568;
            margin: 0;
            font-size: 0.9rem;
          }
        }

        .compatibility-score {
          .score-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &.high-score {
              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            }

            &.low-score {
              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            }

            .score-value {
              color: white;
              font-size: 1.5rem;
              font-weight: 700;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .score-label {
              color: rgba(255, 255, 255, 0.9);
              font-size: 0.7rem;
              font-weight: 500;
            }
          }
        }
      }

      .compatibility-status {
        text-align: center;

        .status-badge {
          display: inline-block;
          padding: 0.5rem 1.5rem;
          border-radius: 20px;
          font-weight: 600;
          font-size: 1rem;

          &.compatible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
          }

          &.incompatible {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
          }
        }
      }
    }

    .iris-characteristics {
      background: rgba(118, 75, 162, 0.1);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      .characteristics-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .characteristic-tag {
          display: inline-block;
          padding: 0.3rem 0.8rem;
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
          color: white;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 500;
        }
      }
    }

    .recommendations, .corrections {
      background: rgba(255, 255, 255, 0.5);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0.5rem;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .corrections {
      border-left: 4px solid #f56565;

      h4 {
        color: #e53e3e;
      }
    }
  }

  .detailed-scores {
    margin: 2rem 0;
    background: var(--background-primary);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);

    h3 {
      color: var(--text-primary);
      margin: 0 0 0.5rem 0;
      font-size: 1.4rem;
      text-align: center;
      font-family: 'Playfair Display', serif;
    }

    .scores-description {
      color: var(--text-secondary);
      text-align: center;
      margin-bottom: 2rem;
      font-size: 1rem;
      line-height: 1.5;
    }

    .scores-grid {
      display: grid;
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .score-item {
      padding: 1.5rem;
      background: var(--background-secondary);
      border-radius: 15px;
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
      box-shadow: var(--shadow-sm);

      &.highest-score {
        border-color: var(--fleur-primary);
        box-shadow: var(--shadow-md);
        transform: translateY(-5px);
      }

      .score-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .score-label {
          font-weight: 600;
          color: var(--text-primary);
          font-size: 1.1rem;
          font-family: 'Playfair Display', serif;
        }

        .score-percentage {
          font-weight: 700;
          color: var(--fleur-primary);
          font-size: 1.2rem;
          background: var(--background-primary);
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
        }
      }

      .score-bar {
        height: 10px;
        background: var(--background-tertiary);
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 1rem;

        .score-fill {
          height: 100%;
          transition: width 1s ease;

          &.flower {
            background: linear-gradient(90deg, var(--fleur-primary), var(--fleur-secondary));
          }

          &.jewel {
            background: linear-gradient(90deg, var(--bijou-primary), var(--bijou-secondary));
          }

          &.shaker {
            background: linear-gradient(90deg, var(--shaker-primary), var(--shaker-secondary));
          }

          &.stream {
            background: linear-gradient(90deg, var(--flux-primary), var(--flux-secondary));
          }
        }
      }

      .score-details {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .score-value {
          font-weight: 600;
          color: var(--text-primary);
          font-size: 1rem;
        }

        .score-description {
          color: var(--text-muted);
          font-size: 0.9rem;
          font-style: italic;
        }
      }
    }

    .score-summary {
      background: var(--background-secondary);
      padding: 1.5rem;
      border-radius: 15px;
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 600;
          color: var(--text-secondary);
          font-size: 1rem;
        }

        .value {
          font-weight: 700;
          font-size: 1.1rem;

          &.dominant {
            color: var(--fleur-primary);
          }

          &.confidence {
            color: var(--success-color);
          }
        }
      }
    }
  }

  .test-info-summary {
    background: rgba(118, 75, 162, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    margin: 2rem 0;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #4a5568;
      }

      .value {
        color: #2d3748;
        font-family: 'Courier New', monospace;
      }
    }
  }

  .results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .personality-test-container {
    padding: 1rem;
  }

  .intro-card, .test-card, .results-card, .loading-card {
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
  }

  .question-text {
    font-size: 1.4rem !important;
  }

  .btn-answer {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-width: 120px !important;
  }

  .profile-name {
    font-size: 1.5rem !important;
  }
}