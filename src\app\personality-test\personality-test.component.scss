// Variables de couleurs - Design corporate professionnel
:root {
  --primary-color: #1a365d;
  --secondary-color: #2d3748;
  --accent-color: #3182ce;
  --success-color: #38a169;
  --warning-color: #d69e2e;
  --danger-color: #e53e3e;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --background-primary: #ffffff;
  --background-secondary: #f7fafc;
  --background-tertiary: #edf2f7;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}

.personality-test-container {
  min-height: 100vh;
  background: var(--background-secondary);
  padding: 2rem 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

// Styles communs pour les cartes
.intro-card, .test-card, .results-card, .loading-card {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 2.5rem;
  max-width: 900px;
  width: 100%;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1.5rem;
    margin: 0 1rem;
  }
}

// Styles communs pour les titres et diviseurs
.title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 0.75rem;
  letter-spacing: -0.02em;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 1.75rem;
  }
}

.subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 400;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }
}

.divider {
  width: 40px;
  height: 2px;
  background: var(--accent-color);
  margin: 0 auto 2rem;
  border-radius: 1px;
}

// Styles pour les boutons
.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  font-family: inherit;
  min-height: 44px;
  white-space: nowrap;

  &.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--secondary-color);
      border-color: var(--secondary-color);
      box-shadow: var(--shadow-sm);
    }

    &:active {
      transform: translateY(1px);
    }
  }

  &.btn-secondary {
    background: var(--background-primary);
    color: var(--text-secondary);
    border-color: var(--border-color);

    &:hover:not(:disabled) {
      background: var(--background-tertiary);
      border-color: var(--text-secondary);
      color: var(--text-primary);
    }
  }

  &.btn-logout {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);

    &:hover:not(:disabled) {
      background: #c53030;
      border-color: #c53030;
    }
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: var(--background-tertiary);
    color: var(--text-muted);
    border-color: var(--border-color);
  }

  @media (max-width: 768px) {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }
}

// Page d'introduction
.test-intro {
  .intro-content {
    margin: 2rem 0;
  }

  .test-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: border-color 0.15s ease;

    &:hover {
      border-color: var(--accent-color);
    }

    .icon {
      font-size: 1.25rem;
      color: var(--accent-color);
      margin-top: 0.125rem;
      flex-shrink: 0;
    }

    .info-text {
      flex: 1;

      h3 {
        margin: 0 0 0.375rem 0;
        color: var(--text-primary);
        font-size: 0.9rem;
        font-weight: 600;
        line-height: 1.3;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.8rem;
        line-height: 1.4;
      }
    }
  }

  .user-info {
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    padding: 1.25rem;
    border-radius: var(--radius-md);

    h3 {
      margin: 0 0 0.75rem 0;
      color: var(--text-primary);
      font-weight: 600;
      font-size: 0.95rem;
    }

    p {
      margin: 0.375rem 0;
      color: var(--text-secondary);
      font-size: 0.85rem;
      line-height: 1.4;

      &.note {
        font-style: italic;
        color: var(--text-muted);
        font-size: 0.8rem;
      }
    }
  }

  .intro-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Interface du test
.test-interface {
  .progress-section {
    margin-bottom: 3rem;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);

    .question-counter {
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .progress-percentage {
      font-weight: 600;
      color: var(--accent-color);
      font-size: 0.8rem;
      background: var(--background-tertiary);
      padding: 0.25rem 0.5rem;
      border-radius: var(--radius-sm);
    }
  }

  .progress-bar {
    width: 100%;
    height: 4px;
    background: var(--background-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 2rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
    transition: width 0.4s ease;
  }

  .question-section {
    text-align: center;
  }

  .question-content {
    margin-bottom: 3rem;
    padding: 2rem 1rem;
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);

    .question-text {
      font-size: 1.25rem;
      font-weight: 500;
      color: var(--text-primary);
      line-height: 1.6;
      margin: 0;
      letter-spacing: -0.01em;

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }
  }

  .answer-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0.75rem;
    }
  }

  .btn-answer {
    flex: 1;
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all 0.15s ease;

    .answer-icon {
      font-size: 1rem;
    }

    &.btn-yes {
      background: var(--success-color);
      color: white;
      border-color: var(--success-color);

      &:hover:not(:disabled) {
        background: #2f855a;
        border-color: #2f855a;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
      }
    }

    &.btn-no {
      background: var(--danger-color);
      color: white;
      border-color: var(--danger-color);

      &:hover:not(:disabled) {
        background: #c53030;
        border-color: #c53030;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
      }
    }
  }
}

// Écran de chargement
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(247, 250, 252, 0.95);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-card {
    text-align: center;
    max-width: 400px;
    padding: 2rem;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 2px solid var(--background-tertiary);
      border-top: 2px solid var(--accent-color);
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
      margin: 0 auto 1.5rem;
    }

    h2 {
      color: var(--text-primary);
      margin-bottom: 0.75rem;
      font-weight: 600;
      font-size: 1.25rem;
    }

    p {
      color: var(--text-secondary);
      margin: 0 0 1.5rem 0;
      font-size: 0.9rem;
    }

    .loading-steps {
      text-align: left;
      margin-top: 1.5rem;
      background: var(--background-primary);
      padding: 1rem;
      border-radius: var(--radius-md);
      border: 1px solid var(--border-color);

      .step {
        padding: 0.375rem 0;
        color: var(--text-secondary);
        font-size: 0.8rem;
        opacity: 0.6;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        &.active {
          opacity: 1;
          font-weight: 500;
          color: var(--accent-color);
        }

        &::before {
          content: "•";
          color: var(--accent-color);
          font-weight: bold;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Résultats du test
.test-results {
  .profile-summary {
    text-align: center;
    margin: 2rem 0;
  }

  .profile-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    background: var(--background-primary);

    // Tous les profils ont le même style épuré
    &.profile-flower,
    &.profile-jewel,
    &.profile-shaker,
    &.profile-stream,
    &.profile-flowerjewel,
    &.profile-jewelshaker,
    &.profile-shakerstream,
    &.profile-streamflower {
      background: var(--background-primary);
      border-color: var(--accent-color);

      &:hover {
        box-shadow: var(--shadow-sm);
      }
    }

    .profile-name {
      color: var(--text-primary);
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
      letter-spacing: -0.02em;
      flex: 1;
    }

    .confidence-score {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 0.25rem;

      .score-label {
        color: var(--text-muted);
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .score-value {
        color: var(--accent-color);
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
      }
    }
  }

  .profile-type {
    margin-bottom: 1.5rem;
    text-align: center;

    .type-badge {
      display: inline-block;
      padding: 0.375rem 0.75rem;
      border-radius: var(--radius-sm);
      font-size: 0.75rem;
      font-weight: 500;
      border: 1px solid var(--border-color);
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &.primary {
        background: var(--background-tertiary);
        color: var(--text-secondary);
        border-color: var(--border-color);
      }

      &.intermediate {
        background: var(--background-tertiary);
        color: var(--text-secondary);
        border-color: var(--border-color);
      }
    }
  }

  .profile-description {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    margin: 1.5rem 0;

    h3 {
      color: var(--text-primary);
      margin: 0 0 0.75rem 0;
      font-weight: 600;
      font-size: 1rem;
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
      margin: 0;
      font-size: 0.9rem;
    }
  }

  .iris-compatibility {
    margin: 2rem 0;

    h3 {
      color: #2d3748;
      margin: 0 0 1.5rem 0;
      text-align: center;
    }

    .compatibility-summary {
      padding: 2rem;
      border-radius: 12px;
      margin-bottom: 2rem;

      &.compatible {
        background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
        border: 2px solid rgba(72, 187, 120, 0.3);
      }

      &.incompatible {
        background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);
        border: 2px solid rgba(245, 101, 101, 0.3);
      }

      .compatibility-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
        }

        .iris-info {
          flex: 1;

          h4 {
            color: #2d3748;
            margin: 0 0 0.5rem 0;
            font-size: 1.2rem;
          }

          p {
            color: #4a5568;
            margin: 0;
            font-size: 0.9rem;
          }
        }

        .compatibility-score {
          .score-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &.high-score {
              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            }

            &.low-score {
              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            }

            .score-value {
              color: white;
              font-size: 1.5rem;
              font-weight: 700;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .score-label {
              color: rgba(255, 255, 255, 0.9);
              font-size: 0.7rem;
              font-weight: 500;
            }
          }
        }
      }

      .compatibility-status {
        text-align: center;

        .status-badge {
          display: inline-block;
          padding: 0.5rem 1.5rem;
          border-radius: 20px;
          font-weight: 600;
          font-size: 1rem;

          &.compatible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
          }

          &.incompatible {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
          }
        }
      }
    }

    .iris-characteristics {
      background: rgba(118, 75, 162, 0.1);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      .characteristics-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .characteristic-tag {
          display: inline-block;
          padding: 0.3rem 0.8rem;
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
          color: white;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 500;
        }
      }
    }

    .recommendations, .corrections {
      background: rgba(255, 255, 255, 0.5);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0.5rem;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .corrections {
      border-left: 4px solid #f56565;

      h4 {
        color: #e53e3e;
      }
    }
  }

  .detailed-scores {
    margin: 2rem 0;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;

    .scores-header {
      padding: 1.5rem;
      background: var(--background-secondary);
      border-bottom: 1px solid var(--border-color);

      h3 {
        color: var(--text-primary);
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
      }

      .scores-description {
        color: var(--text-secondary);
        margin: 0;
        font-size: 0.875rem;
      }
    }

    .scores-grid {
      padding: 1.5rem;
      display: grid;
      gap: 1rem;
    }

    .score-item {
      padding: 1rem;
      background: var(--background-secondary);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      transition: all 0.15s ease;

      &.highest-score {
        border-color: var(--accent-color);
        background: var(--background-primary);
        box-shadow: var(--shadow-sm);
      }

      .score-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;

        .score-label {
          font-weight: 600;
          color: var(--text-primary);
          font-size: 0.9rem;
        }

        .score-percentage {
          font-weight: 700;
          color: var(--accent-color);
          font-size: 1rem;
          background: var(--background-tertiary);
          padding: 0.125rem 0.375rem;
          border-radius: var(--radius-sm);
        }
      }

      .score-bar {
        height: 6px;
        background: var(--background-tertiary);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 0.75rem;

        .score-fill {
          height: 100%;
          transition: width 0.8s ease;
          background: var(--accent-color);

          // Toutes les barres ont la même couleur professionnelle
          &.flower,
          &.jewel,
          &.shaker,
          &.stream {
            background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
          }
        }
      }

      .score-details {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .score-value {
          font-weight: 500;
          color: var(--text-primary);
          font-size: 0.8rem;
        }

        .score-description {
          color: var(--text-muted);
          font-size: 0.75rem;
        }
      }
    }

    .score-summary {
      padding: 1.5rem;
      background: var(--background-tertiary);
      border-top: 1px solid var(--border-color);

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: var(--text-secondary);
          font-size: 0.875rem;
        }

        .value {
          font-weight: 600;
          font-size: 0.875rem;

          &.dominant {
            color: var(--accent-color);
          }

          &.confidence {
            color: var(--success-color);
          }
        }
      }
    }
  }

  .test-info-summary {
    background: rgba(118, 75, 162, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    margin: 2rem 0;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #4a5568;
      }

      .value {
        color: #2d3748;
        font-family: 'Courier New', monospace;
      }
    }
  }

  .results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .personality-test-container {
    padding: 1rem;
  }

  .intro-card, .test-card, .results-card, .loading-card {
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
  }

  .question-text {
    font-size: 1.4rem !important;
  }

  .btn-answer {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-width: 120px !important;
  }

  .profile-name {
    font-size: 1.5rem !important;
  }
}