.personality-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Poppins', sans-serif;
}

// Styles communs pour les cartes
.intro-card, .test-card, .results-card, .loading-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

// Styles communs pour les titres et diviseurs
.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  text-align: center;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #718096;
  text-align: center;
  margin-bottom: 2rem;
}

.divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  margin: 0 auto 2rem;
  border-radius: 2px;
}

// Styles pour les boutons
.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    }
  }

  &.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;

    &:hover {
      background: #cbd5e0;
      transform: translateY(-1px);
    }
  }

  &.btn-logout {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(245, 101, 101, 0.4);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
}

// Page d'introduction
.test-intro {
  .intro-content {
    margin: 2rem 0;
  }

  .test-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 12px;

    .icon {
      font-size: 2rem;
    }

    .info-text {
      h3 {
        margin: 0 0 0.5rem 0;
        color: #2d3748;
        font-size: 1.1rem;
      }

      p {
        margin: 0;
        color: #718096;
        font-size: 0.9rem;
      }
    }
  }

  .user-info {
    background: rgba(118, 75, 162, 0.1);
    padding: 1.5rem;
    border-radius: 12px;

    h3 {
      margin: 0 0 1rem 0;
      color: #2d3748;
    }

    p {
      margin: 0.5rem 0;
      color: #4a5568;

      &.note {
        font-style: italic;
        color: #718096;
        font-size: 0.9rem;
      }
    }
  }

  .intro-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Interface du test
.test-interface {
  .progress-section {
    margin-bottom: 3rem;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .question-counter {
      font-weight: 600;
      color: #4a5568;
    }

    .progress-percentage {
      font-weight: 700;
      color: #667eea;
    }
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
  }

  .question-section {
    text-align: center;
  }

  .question-content {
    margin-bottom: 3rem;

    .question-text {
      font-size: 1.8rem;
      font-weight: 600;
      color: #2d3748;
      line-height: 1.4;
      margin: 0;
    }
  }

  .answer-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }
  }

  .btn-answer {
    padding: 1.5rem 3rem;
    font-size: 1.3rem;
    min-width: 150px;

    .answer-icon {
      font-size: 1.5rem;
    }

    &.btn-yes {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(72, 187, 120, 0.4);
      }
    }

    &.btn-no {
      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
      color: white;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(245, 101, 101, 0.4);
      }
    }
  }
}

// Écran de chargement
.loading-screen {
  .loading-card {
    text-align: center;

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid #e2e8f0;
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 2rem;
    }

    h2 {
      color: #2d3748;
      margin-bottom: 1rem;
    }

    p {
      color: #718096;
      margin: 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Résultats du test
.test-results {
  .profile-summary {
    text-align: center;
    margin: 2rem 0;
  }

  .profile-badge {
    display: inline-block;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 1rem;

    &.profile-flower {
      background: linear-gradient(135deg, #fbb6ce 0%, #f687b3 100%);
    }

    &.profile-jewel {
      background: linear-gradient(135deg, #90cdf4 0%, #63b3ed 100%);
    }

    &.profile-shaker {
      background: linear-gradient(135deg, #fbd38d 0%, #f6ad55 100%);
    }

    &.profile-stream {
      background: linear-gradient(135deg, #9ae6b4 0%, #68d391 100%);
    }

    &.profile-flowerjewel {
      background: linear-gradient(135deg, #fbb6ce 0%, #90cdf4 100%);
    }

    &.profile-jewelshaker {
      background: linear-gradient(135deg, #90cdf4 0%, #fbd38d 100%);
    }

    &.profile-shakerstream {
      background: linear-gradient(135deg, #fbd38d 0%, #9ae6b4 100%);
    }

    &.profile-streamflower {
      background: linear-gradient(135deg, #9ae6b4 0%, #fbb6ce 100%);
    }

    .profile-name {
      color: white;
      font-size: 2rem;
      font-weight: 700;
      margin: 0 0 1rem 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .confidence-score {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .score-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.9rem;
        font-weight: 500;
      }

      .score-value {
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .profile-type {
    margin-bottom: 2rem;

    .type-badge {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;

      &.primary {
        background: rgba(102, 126, 234, 0.2);
        color: #667eea;
      }

      &.intermediate {
        background: rgba(118, 75, 162, 0.2);
        color: #764ba2;
      }
    }
  }

  .profile-description {
    background: rgba(102, 126, 234, 0.1);
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;

    h3 {
      color: #2d3748;
      margin: 0 0 1rem 0;
    }

    p {
      color: #4a5568;
      line-height: 1.6;
      margin: 0;
      font-size: 1.1rem;
    }
  }

  .iris-compatibility {
    margin: 2rem 0;

    h3 {
      color: #2d3748;
      margin: 0 0 1.5rem 0;
      text-align: center;
    }

    .compatibility-summary {
      padding: 2rem;
      border-radius: 12px;
      margin-bottom: 2rem;

      &.compatible {
        background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
        border: 2px solid rgba(72, 187, 120, 0.3);
      }

      &.incompatible {
        background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);
        border: 2px solid rgba(245, 101, 101, 0.3);
      }

      .compatibility-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1rem;
        }

        .iris-info {
          flex: 1;

          h4 {
            color: #2d3748;
            margin: 0 0 0.5rem 0;
            font-size: 1.2rem;
          }

          p {
            color: #4a5568;
            margin: 0;
            font-size: 0.9rem;
          }
        }

        .compatibility-score {
          .score-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &.high-score {
              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            }

            &.low-score {
              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            }

            .score-value {
              color: white;
              font-size: 1.5rem;
              font-weight: 700;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .score-label {
              color: rgba(255, 255, 255, 0.9);
              font-size: 0.7rem;
              font-weight: 500;
            }
          }
        }
      }

      .compatibility-status {
        text-align: center;

        .status-badge {
          display: inline-block;
          padding: 0.5rem 1.5rem;
          border-radius: 20px;
          font-weight: 600;
          font-size: 1rem;

          &.compatible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
          }

          &.incompatible {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
          }
        }
      }
    }

    .iris-characteristics {
      background: rgba(118, 75, 162, 0.1);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      .characteristics-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .characteristic-tag {
          display: inline-block;
          padding: 0.3rem 0.8rem;
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
          color: white;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 500;
        }
      }
    }

    .recommendations, .corrections {
      background: rgba(255, 255, 255, 0.5);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;

      h4 {
        color: #2d3748;
        margin: 0 0 1rem 0;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0.5rem;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .corrections {
      border-left: 4px solid #f56565;

      h4 {
        color: #e53e3e;
      }
    }
  }

  .detailed-scores {
    margin: 2rem 0;

    h3 {
      color: #2d3748;
      margin: 0 0 1.5rem 0;
    }

    .scores-grid {
      display: grid;
      gap: 1rem;
    }

    .score-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 8px;

      .score-label {
        min-width: 80px;
        font-weight: 600;
        color: #4a5568;
      }

      .score-bar {
        flex: 1;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;

        .score-fill {
          height: 100%;
          transition: width 0.5s ease;

          &.flower {
            background: linear-gradient(90deg, #fbb6ce, #f687b3);
          }

          &.jewel {
            background: linear-gradient(90deg, #90cdf4, #63b3ed);
          }

          &.shaker {
            background: linear-gradient(90deg, #fbd38d, #f6ad55);
          }

          &.stream {
            background: linear-gradient(90deg, #9ae6b4, #68d391);
          }
        }
      }

      .score-value {
        min-width: 40px;
        font-weight: 600;
        color: #2d3748;
        text-align: right;
      }
    }
  }

  .test-info-summary {
    background: rgba(118, 75, 162, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    margin: 2rem 0;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #4a5568;
      }

      .value {
        color: #2d3748;
        font-family: 'Courier New', monospace;
      }
    }
  }

  .results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .personality-test-container {
    padding: 1rem;
  }

  .intro-card, .test-card, .results-card, .loading-card {
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
  }

  .question-text {
    font-size: 1.4rem !important;
  }

  .btn-answer {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-width: 120px !important;
  }

  .profile-name {
    font-size: 1.5rem !important;
  }
}