// Variables - Utilisation des variables globales pour uniformiser le thème
$primary-color: var(--fleur-primary);
$secondary-color: var(--bijou-primary);
$primary-gradient: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
$accent-color: var(--flux-primary);
$light-bg: #f5f7fa;
$card-bg: #ffffff;
$text-color: #333;
$text-light: #666;
$success-color: var(--flux-primary);
$warning-color: var(--shaker-primary);
$danger-color: #e63757;
$border-radius: 12px;
$box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);

// Styles généraux
.dashboard-container {
  font-family: 'Montserrat', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 20px;
  min-height: 100vh;
  color: $text-color;
}

// En-tête du tableau de bord
.dashboard-header {
  margin-bottom: 30px;

  .welcome-card {
    background: $card-bg;
    color: $text-color;
    padding: 30px;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-left: 4px solid $primary-color;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 30%;
      height: 100%;
      background: linear-gradient(to right, rgba(44, 123, 229, 0), rgba(44, 123, 229, 0.1));
      z-index: 0;
    }

    h1 {
      font-size: 1.8rem;
      margin: 0 0 5px 0;
      font-weight: 700;
      color: $text-color;
      position: relative;
      z-index: 1;
    }

    p {
      margin: 0 0 20px 0;
      color: $text-light;
      position: relative;
      z-index: 1;
    }

    .new-scan-btn {
      background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 50px;
      font-weight: 600;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
      box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);

      i {
        margin-right: 10px;
        font-size: 1.1rem;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);
      }

      &:active {
        transform: translateY(-2px);
      }
    }
  }
}

// Cartes d'information
.info-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;

  .info-card {
    background-color: $card-bg;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    padding: 20px;
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid transparent;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 1rem 2rem rgba(18, 38, 63, 0.075);
    }

    &:nth-child(1) {
      border-bottom-color: $primary-color;
    }

    &:nth-child(2) {
      border-bottom-color: $success-color;
    }

    &:nth-child(3) {
      border-bottom-color: $warning-color;
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      color: white;
      font-size: 1.2rem;
      transition: transform 0.2s ease;

      &.security {
        background-color: $primary-color;
      }

      &.verified {
        background-color: $success-color;
      }

      &.time {
        background-color: $warning-color;
      }
    }

    &:hover .card-icon {
      transform: scale(1.1);
    }

    .card-content {
      h3 {
        font-size: 0.85rem;
        margin: 0 0 5px 0;
        color: $text-light;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .status {
        font-size: 1.2rem;
        font-weight: 700;
        margin: 0;
        color: $text-color;
      }
    }
  }
}

// Navigation du tableau de bord
.dashboard-nav {
  display: flex;
  margin-bottom: 30px;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;

  .nav-btn {
    background: none;
    border: none;
    padding: 16px 25px;
    font-size: 0.95rem;
    font-weight: 600;
    color: $text-light;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      margin-right: 8px;
      font-size: 1rem;
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }

    &:after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 0;
      height: 2px;
      background-color: var(--fleur-primary);
      transition: width 0.3s ease;
    }

    &:hover {
      color: var(--fleur-primary);

      i {
        opacity: 1;
      }

      &:after {
        width: 100%;
      }
    }

    &.active {
      color: var(--fleur-primary);

      i {
        opacity: 1;
      }

      &:after {
        width: 100%;
      }
    }
  }
}

// Aperçu du profil d'iris
.profile-overview {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 25px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(44, 123, 229, 0.1) 0%, rgba(44, 123, 229, 0) 70%);
    border-radius: 0 0 0 100%;
  }

  .section-header {
    margin-bottom: 25px;
    position: relative;

    h2 {
      font-size: 1.4rem;
      margin: 0 0 5px 0;
      font-weight: 700;
      color: $text-color;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 20px;
        background-color: $primary-color;
        margin-right: 10px;
        border-radius: 2px;
      }
    }

    p {
      color: $text-light;
      margin: 0 0 0 14px;
      font-size: 0.9rem;
    }
  }

  .profile-content {
    display: flex;
    gap: 30px;

    .iris-image-container {
      position: relative;
      width: 220px;
      height: 220px;
      border-radius: 8px;
      overflow: hidden;
      background-color: #f0f0f0;
      box-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.15);
      }

      .iris-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      &:hover .iris-image {
        transform: scale(1.05);
      }

      .verification-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: $success-color;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 5px rgba(0, 217, 126, 0.3);

        &::before {
          content: '✓';
          margin-right: 5px;
          font-weight: bold;
        }
      }

      .iris-id {
        position: absolute;
        bottom: 10px;
        left: 10px;
        color: white;
        font-size: 0.8rem;
        background-color: rgba(18, 38, 63, 0.8);
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
    }

    .iris-details {
      flex: 1;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        cursor: pointer;
        padding: 5px;
        border-radius: 6px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(44, 123, 229, 0.05);
        }

        .detail-label {
          width: 180px;
          font-size: 0.9rem;
          color: $text-light;
          font-weight: 500;
        }

        .detail-bar {
          flex: 1;
          height: 8px;
          background-color: #edf2f9;
          border-radius: 4px;
          margin: 0 20px;
          overflow: hidden;
          position: relative;

          .progress-bar {
            height: 100%;
            background: $primary-gradient;
            width: 100%;
            border-radius: 4px;
            position: relative;
            overflow: hidden;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0) 100%
              );
              animation: shimmer 2s infinite;
            }
          }
        }

        .detail-value {
          width: 120px;
          text-align: right;
          font-weight: 700;
          color: $text-color;
        }
      }

      .verification-info {
        margin-top: 30px;
        padding: 15px;
        border-radius: 6px;
        background-color: #edf2f9;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: darken(#edf2f9, 2%);
        }

        .verification-label {
          color: $text-light;
          font-size: 0.9rem;
          margin-right: 15px;
          font-weight: 500;
        }

        .verification-time {
          font-weight: 600;
          margin-right: 20px;
          color: $text-color;
        }

        .verification-status {
          margin-left: auto;
          display: flex;
          align-items: center;
          font-weight: 600;
          padding: 5px 10px;
          border-radius: 4px;

          &.success {
            color: white;
            background-color: $success-color;
          }

          i {
            margin-right: 5px;
          }
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Sections inférieures
.bottom-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  .activity-section, .security-section {
    background-color: $card-bg;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    padding: 25px;
    position: relative;
    overflow: hidden;

    .section-header {
      margin-bottom: 25px;
      position: relative;

      h2 {
        font-size: 1.4rem;
        margin: 0 0 5px 0;
        font-weight: 700;
        color: $text-color;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 20px;
          background-color: $primary-color;
          margin-right: 10px;
          border-radius: 2px;
        }
      }

      p {
        color: $text-light;
        margin: 0 0 0 14px;
        font-size: 0.9rem;
      }
    }
  }

  // Activité récente
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 8px;
      transition: all 0.2s ease;
      cursor: pointer;
      margin-bottom: 10px;
      border-left: 3px solid transparent;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background-color: #f9fbfd;
        transform: translateX(5px);
      }

      &:nth-child(1) {
        border-left-color: $secondary-color;

        &:hover {
          background-color: rgba(110, 0, 255, 0.05);
        }
      }

      &:nth-child(2) {
        border-left-color: #ff5c8d;

        &:hover {
          background-color: rgba(255, 92, 141, 0.05);
        }
      }

      &:nth-child(3) {
        border-left-color: $warning-color;

        &:hover {
          background-color: rgba(246, 195, 67, 0.05);
        }
      }

      .activity-icon {
        width: 42px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: white;
        font-size: 1rem;
        transition: transform 0.2s ease;
        box-shadow: 0 0.25rem 0.5rem rgba(18, 38, 63, 0.1);

        &.scan {
          background-color: $secondary-color;
        }

        &.profile {
          background-color: #ff5c8d;
        }

        &.report {
          background-color: $warning-color;
        }
      }

      &:hover .activity-icon {
        transform: scale(1.1);
      }

      .activity-details {
        flex: 1;

        h4 {
          margin: 0 0 5px 0;
          font-size: 1rem;
          font-weight: 600;
          color: $text-color;
        }

        p {
          margin: 0;
          color: $text-light;
          font-size: 0.85rem;
          display: flex;
          align-items: center;

          &::before {
            content: '🕒';
            margin-right: 5px;
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  // Statut de sécurité
  .security-list {
    .security-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 8px;
      transition: all 0.2s ease;
      cursor: pointer;
      margin-bottom: 10px;
      border-left: 3px solid transparent;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background-color: #f9fbfd;
        transform: translateX(5px);
      }

      &:nth-child(1) {
        border-left-color: $success-color;

        &:hover {
          background-color: rgba(0, 217, 126, 0.05);
        }
      }

      &:nth-child(2) {
        border-left-color: $secondary-color;

        &:hover {
          background-color: rgba(110, 0, 255, 0.05);
        }
      }

      &:nth-child(3) {
        border-left-color: $primary-color;

        &:hover {
          background-color: rgba(44, 123, 229, 0.05);
        }
      }

      .security-icon {
        width: 42px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: white;
        font-size: 1rem;
        transition: transform 0.2s ease;
        box-shadow: 0 0.25rem 0.5rem rgba(18, 38, 63, 0.1);

        &.data {
          background-color: $success-color;
        }

        &.biometric {
          background-color: $secondary-color;
        }

        &.compliance {
          background-color: $primary-color;
        }
      }

      &:hover .security-icon {
        transform: scale(1.1);
      }

      .security-details {
        flex: 1;

        h4 {
          margin: 0 0 5px 0;
          font-size: 1rem;
          font-weight: 600;
          color: $text-color;
        }

        p {
          margin: 0;
          color: $text-light;
          font-size: 0.85rem;
        }
      }
    }
  }
}

// Responsive
@media (max-width: 1200px) {
  .profile-content {
    flex-direction: column;

    .iris-image-container {
      margin: 0 auto 30px;
      width: 280px;
      height: 280px;
    }
  }
}

@media (max-width: 1024px) {
  .bottom-sections {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .dashboard-container {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .info-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .dashboard-nav {
    overflow-x: auto;
    white-space: nowrap;
    padding: 5px;

    .nav-btn {
      padding: 12px 15px;
      font-size: 0.85rem;

      i {
        margin-right: 5px;
      }
    }
  }

  .profile-overview,
  .activity-section,
  .security-section {
    padding: 20px 15px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start !important;

    .detail-label {
      width: 100% !important;
      margin-bottom: 8px;
    }

    .detail-bar {
      width: 100% !important;
      margin: 8px 0 !important;
    }

    .detail-value {
      width: 100% !important;
      text-align: left !important;
      margin-top: 8px;
    }
  }

  .verification-info {
    flex-direction: column;
    align-items: flex-start !important;

    .verification-label,
    .verification-time {
      margin-bottom: 10px;
      margin-right: 0 !important;
    }

    .verification-status {
      margin-left: 0 !important;
      align-self: flex-start;
    }
  }

  .section-header {
    h2 {
      font-size: 1.2rem !important;
    }

    p {
      font-size: 0.85rem !important;
    }
  }
}

@media (max-width: 480px) {
  .welcome-card {
    h1 {
      font-size: 1.5rem !important;
    }

    p {
      font-size: 0.9rem !important;
    }

    .new-scan-btn {
      width: 100%;
      justify-content: center;
      padding: 12px 15px !important;
    }
  }

  .dashboard-nav {
    .nav-btn {
      padding: 10px !important;
      font-size: 0.8rem !important;

      i {
        margin-right: 3px !important;
      }
    }
  }

  .iris-image-container {
    width: 100% !important;
    height: 220px !important;
  }
}