import { Component } from '@angular/core';
import { Router } from '@angular/router';

interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  termsAccepted: boolean;
}

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss']
})
export class SignupComponent {
  signupData: SignupData = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    termsAccepted: false
  };

  showPassword: boolean = false;
  isLoading: boolean = false;

  constructor(private router: Router) {}

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onSubmit(): void {
    if (this.isLoading) return;

    // Vérifier que les mots de passe correspondent
    if (this.signupData.password !== this.signupData.confirmPassword) {
      return;
    }

    this.isLoading = true;

    // Simuler une inscription avec un délai
    setTimeout(() => {
      console.log('Tentative d\'inscription avec:', this.signupData);

      // Ici, vous implémenteriez la logique réelle d'inscription
      // Pour l'instant, nous simulons simplement une inscription réussie

      // Rediriger vers la page de connexion après inscription
      this.router.navigate(['/login']);

      this.isLoading = false;
    }, 1500);
  }
}
