import { Injectable } from '@angular/core';
import { Observable, of, delay } from 'rxjs';
import { TestSession } from '../models/personality-test.model';

@Injectable({
  providedIn: 'root'
})
export class MockFirebaseService {
  private testSessions: TestSession[] = [];
  private nextId = 1;

  constructor() {
    // Charger les données depuis localStorage si disponibles
    this.loadFromLocalStorage();
  }

  /**
   * Simule la sauvegarde d'une session de test
   */
  saveTestSession(session: TestSession): Observable<string> {
    const sessionId = `test_${this.nextId++}`;
    const sessionWithId = { ...session, id: sessionId };
    
    this.testSessions.push(sessionWithId);
    this.saveToLocalStorage();
    
    console.log('Session sauvegardée (Mock):', sessionWithId);
    
    // Simuler un délai réseau
    return of(sessionId).pipe(delay(1000));
  }

  /**
   * Simule la récupération de toutes les sessions
   */
  getAllTestSessions(): Observable<TestSession[]> {
    return of([...this.testSessions]).pipe(delay(500));
  }

  /**
   * Simule la récupération des sessions d'un utilisateur
   */
  getUserTestSessions(userEmail: string): Observable<TestSession[]> {
    const userSessions = this.testSessions.filter(session => session.userEmail === userEmail);
    return of(userSessions).pipe(delay(500));
  }

  /**
   * Sauvegarde dans localStorage pour persistance
   */
  private saveToLocalStorage(): void {
    try {
      localStorage.setItem('pfa_test_sessions', JSON.stringify(this.testSessions));
      localStorage.setItem('pfa_next_id', this.nextId.toString());
    } catch (error) {
      console.warn('Impossible de sauvegarder dans localStorage:', error);
    }
  }

  /**
   * Charge depuis localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const sessions = localStorage.getItem('pfa_test_sessions');
      const nextId = localStorage.getItem('pfa_next_id');
      
      if (sessions) {
        this.testSessions = JSON.parse(sessions);
      }
      
      if (nextId) {
        this.nextId = parseInt(nextId, 10);
      }
    } catch (error) {
      console.warn('Impossible de charger depuis localStorage:', error);
    }
  }

  /**
   * Efface toutes les données (pour les tests)
   */
  clearAllData(): void {
    this.testSessions = [];
    this.nextId = 1;
    localStorage.removeItem('pfa_test_sessions');
    localStorage.removeItem('pfa_next_id');
  }

  /**
   * Obtient les statistiques des tests
   */
  getTestStatistics(): Observable<any> {
    const stats = {
      totalTests: this.testSessions.length,
      profileDistribution: this.calculateProfileDistribution(),
      averageConfidenceScore: this.calculateAverageConfidence(),
      recentTests: this.testSessions.slice(-5).reverse()
    };
    
    return of(stats).pipe(delay(300));
  }

  private calculateProfileDistribution(): Record<string, number> {
    const distribution: Record<string, number> = {};
    
    this.testSessions.forEach(session => {
      const profile = session.finalProfile.primaryClass;
      distribution[profile] = (distribution[profile] || 0) + 1;
    });
    
    return distribution;
  }

  private calculateAverageConfidence(): number {
    if (this.testSessions.length === 0) return 0;
    
    const totalConfidence = this.testSessions.reduce(
      (sum, session) => sum + session.finalProfile.confidenceScore, 
      0
    );
    
    return Math.round(totalConfidence / this.testSessions.length);
  }
}
