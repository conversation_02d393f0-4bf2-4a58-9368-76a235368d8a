// Variables
$footer-bg: #f9fafb;
$footer-text: #6c757d;
$footer-heading: #495057;
$footer-link: #6c757d;
$footer-link-hover: #007bff;
$footer-border: #e9ecef;

.footer {
  background-color: $footer-bg;
  color: $footer-text;
  padding: 0;
  margin-top: 30px;
  border-top: 1px solid $footer-border;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.8rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  padding: 20px 0 10px;
  border-bottom: 1px solid $footer-border;
}

.footer-logo {
  flex: 1;
  min-width: 200px;
  margin-bottom: 10px;

  h2 {
    font-size: 1.2rem;
    margin: 0 0 3px 0;
    color: $footer-heading;
    font-weight: 600;
  }

  p {
    font-size: 0.75rem;
    margin: 0;
    color: $footer-text;
    max-width: 250px;
    font-style: italic;
  }
}

.footer-sections {
  display: flex;
  flex-wrap: wrap;
  flex: 2;
  justify-content: flex-end;
}

.footer-section {
  margin-left: 40px;
  margin-bottom: 10px;

  h3 {
    font-size: 0.7rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: $footer-heading;
    letter-spacing: 0.5px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 5px;

      a {
        color: $footer-link;
        text-decoration: none;
        font-size: 0.75rem;
        transition: color 0.2s ease;

        &:hover {
          color: $footer-link-hover;
        }
      }
    }
  }
}

.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.social-icons {
  display: flex;
  gap: 8px;

  .social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: $footer-text;
    text-decoration: none;
    transition: color 0.2s ease;

    i {
      font-size: 0.8rem;
    }

    &:hover {
      color: $footer-link-hover;
    }
  }
}

.copyright {
  p {
    margin: 0;
    font-size: 0.7rem;
    color: $footer-text;
  }
}

// Responsive styles
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
  }

  .footer-sections {
    flex-direction: row;
    justify-content: space-between;
    margin-top: 15px;
  }

  .footer-section {
    margin-left: 0;
    margin-right: 15px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .social-icons {
    margin: 0 auto 10px;
  }
}

@media (max-width: 576px) {
  .footer-sections {
    flex-direction: column;
  }

  .footer-section {
    margin-bottom: 15px;
  }
}